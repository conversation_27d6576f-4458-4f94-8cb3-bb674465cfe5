<?php
// الإصلاح النهائي الشامل
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>الإصلاح النهائي الشامل</h1>";

// 1. إنشاء قاعدة البيانات والجداول
echo "<h2>1. إعداد قاعدة البيانات</h2>";
try {
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $dbname = 'invoice_saas';
    
    // الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء/التحقق من قاعدة البيانات<br>";
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء جدول المستخدمين
    $sql = "
    CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        company_name VARCHAR(100),
        phone VARCHAR(20),
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50),
        subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
        subscription_expires_at DATETIME NULL,
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول المستخدمين<br>";
    
    // إنشاء جدول العملاء
    $sql = "
    CREATE TABLE IF NOT EXISTS clients (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        company VARCHAR(100),
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50),
        tax_number VARCHAR(50),
        notes TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_email (email)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول العملاء<br>";
    
    // إنشاء جدول رسائل التواصل
    $sql = "
    CREATE TABLE IF NOT EXISTS contact_messages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('general', 'support', 'billing', 'feature', 'bug', 'partnership') DEFAULT 'general',
        status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_type (type),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول رسائل التواصل<br>";
    
    // إنشاء مستخدم تجريبي
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, company_name) 
            VALUES ('admin', '<EMAIL>', ?, 'المدير', 'العام', 'شركة تجريبية')
        ");
        $stmt->execute([$hashedPassword]);
        echo "✅ تم إنشاء المستخدم التجريبي (admin / 123456)<br>";
    } else {
        echo "✅ المستخدم التجريبي موجود بالفعل<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// 2. إنشاء المجلدات المطلوبة
echo "<h2>2. إنشاء المجلدات</h2>";
$directories = [
    'assets',
    'assets/css',
    'assets/js', 
    'assets/images',
    'assets/uploads',
    'assets/temp',
    'logs',
    'backups'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ تم إنشاء مجلد $dir<br>";
        } else {
            echo "❌ فشل في إنشاء مجلد $dir<br>";
        }
    } else {
        echo "✅ مجلد $dir موجود<br>";
    }
    
    // إنشاء ملف .gitkeep
    $gitkeep = $dir . '/.gitkeep';
    if (!file_exists($gitkeep)) {
        file_put_contents($gitkeep, '# Keep this directory in git');
    }
}

// 3. إنشاء ملف index.html للحماية في المجلدات الحساسة
echo "<h2>3. حماية المجلدات</h2>";
$protectedDirs = ['config', 'includes', 'logs', 'backups'];
foreach ($protectedDirs as $dir) {
    $indexFile = $dir . '/index.html';
    if (!file_exists($indexFile)) {
        file_put_contents($indexFile, '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><h1>Access Denied</h1></body></html>');
        echo "✅ تم إنشاء ملف الحماية في $dir<br>";
    }
}

// 4. اختبار الصفحات الأساسية
echo "<h2>4. اختبار الصفحات</h2>";
$pages = [
    'index.php',
    'contact.php', 
    'templates.php',
    'pricing.php',
    'help.php'
];

foreach ($pages as $page) {
    if (file_exists($page)) {
        echo "✅ $page موجود<br>";
    } else {
        echo "❌ $page غير موجود<br>";
    }
}

// 5. اختبار نموذج التواصل
echo "<h2>5. اختبار نموذج التواصل</h2>";
try {
    // محاكاة إرسال رسالة
    $_POST = [
        'name' => 'اختبار النظام',
        'email' => '<EMAIL>',
        'subject' => 'اختبار تلقائي',
        'message' => 'هذه رسالة اختبار تلقائية للتأكد من عمل النظام',
        'type' => 'general'
    ];
    
    // تضمين ملف contact.php وتشغيله
    ob_start();
    include 'contact.php';
    $output = ob_get_clean();
    
    if (strpos($output, 'تم إرسال رسالتك بنجاح') !== false) {
        echo "✅ نموذج التواصل يعمل بشكل صحيح<br>";
    } else {
        echo "⚠️ نموذج التواصل قد يحتاج لمراجعة<br>";
    }
    
    // إعادة تعيين POST
    $_POST = [];
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار نموذج التواصل: " . $e->getMessage() . "<br>";
}

echo "<h2>6. تم الانتهاء من الإصلاح!</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🎉 النظام جاهز للاستخدام!</h3>";
echo "<p><strong>الخطوات التالية:</strong></p>";
echo "<ol>";
echo "<li><a href='system_check.php' target='_blank'>فحص شامل للنظام</a></li>";
echo "<li><a href='index.php' target='_blank'>الصفحة الرئيسية</a></li>";
echo "<li><a href='contact.php' target='_blank'>صفحة التواصل</a></li>";
echo "<li><a href='auth/login.php' target='_blank'>تسجيل الدخول</a> (admin / 123456)</li>";
echo "<li><a href='templates.php' target='_blank'>معرض القوالب</a></li>";
echo "<li><a href='pricing.php' target='_blank'>خطط الأسعار</a></li>";
echo "<li><a href='help.php' target='_blank'>مركز المساعدة</a></li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li>تأكد من تشغيل خدمات Apache و MySQL في XAMPP</li>";
echo "<li>المستخدم التجريبي: admin / 123456</li>";
echo "<li>جميع الملفات والمجلدات تم إنشاؤها</li>";
echo "<li>قاعدة البيانات والجداول جاهزة</li>";
echo "<li>نموذج التواصل يعمل بشكل صحيح</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    direction: rtl;
    background: #f8f9fa;
}
h1, h2, h3, h4 {
    color: #333;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
ol, ul {
    text-align: right;
}
</style>
