<?php
// إصلاح الربط مع قاعدة البيانات الأصلية
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح الربط مع قاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }";
echo ".error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }";
echo ".info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }";
echo ".warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }";
echo "h1, h2 { color: #333; }";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 إصلاح الربط مع قاعدة البيانات الأصلية</h1>";

// الخطوة 1: اختبار الاتصال الأساسي
echo "<h2>الخطوة 1: اختبار الاتصال الأساسي بـ MySQL</h2>";

$host = 'localhost';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='result success'>✅ الاتصال بـ MySQL نجح</div>";
    
    // عرض قواعد البيانات الموجودة
    echo "<h3>قواعد البيانات الموجودة:</h3>";
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='result info'>";
    echo "<strong>قواعد البيانات المتاحة:</strong><br>";
    foreach ($databases as $db) {
        if (!in_array($db, ['information_schema', 'mysql', 'performance_schema', 'sys'])) {
            echo "📁 $db<br>";
        }
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div class='result error'>❌ فشل الاتصال بـ MySQL: " . $e->getMessage() . "</div>";
    echo "<div class='result warning'>";
    echo "<h4>تأكد من:</h4>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL في XAMPP/WAMP</li>";
    echo "<li>أن المنفذ 3306 مفتوح</li>";
    echo "<li>أن اسم المستخدم وكلمة المرور صحيحة</li>";
    echo "</ul>";
    echo "</div>";
    exit;
}

// الخطوة 2: إنشاء أو استخدام قاعدة البيانات
echo "<h2>الخطوة 2: إعداد قاعدة البيانات</h2>";

$dbname = 'invoice_saas';

// التحقق من وجود قاعدة البيانات
$stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
if ($stmt->rowCount() > 0) {
    echo "<div class='result info'>📁 قاعدة البيانات '$dbname' موجودة بالفعل</div>";
} else {
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='result success'>✅ تم إنشاء قاعدة البيانات '$dbname'</div>";
}

// الاتصال بقاعدة البيانات
$pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات '$dbname'</div>";

// الخطوة 3: فحص الجداول الموجودة
echo "<h2>الخطوة 3: فحص الجداول الموجودة</h2>";

$stmt = $pdo->query("SHOW TABLES");
$existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);

if (empty($existingTables)) {
    echo "<div class='result warning'>⚠️ لا توجد جداول في قاعدة البيانات</div>";
} else {
    echo "<div class='result info'>";
    echo "<strong>الجداول الموجودة:</strong><br>";
    foreach ($existingTables as $table) {
        echo "📋 $table<br>";
    }
    echo "</div>";
}

// الخطوة 4: إنشاء الجداول المطلوبة
echo "<h2>الخطوة 4: إنشاء الجداول المطلوبة</h2>";

$requiredTables = [
    'users' => "
    CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        company_name VARCHAR(100),
        phone VARCHAR(20),
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50),
        subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
        subscription_expires_at DATETIME NULL,
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_subscription (subscription_plan)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'clients' => "
    CREATE TABLE IF NOT EXISTS clients (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        company VARCHAR(100),
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50),
        tax_number VARCHAR(50),
        notes TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_email (email)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'invoices' => "
    CREATE TABLE IF NOT EXISTS invoices (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        client_id INT NOT NULL,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        title VARCHAR(255),
        issue_date DATE NOT NULL,
        due_date DATE,
        status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
        subtotal DECIMAL(10,2) DEFAULT 0,
        tax_rate DECIMAL(5,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'SAR',
        notes TEXT,
        terms TEXT,
        template_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_client_id (client_id),
        INDEX idx_status (status),
        INDEX idx_invoice_number (invoice_number)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'invoice_items' => "
    CREATE TABLE IF NOT EXISTS invoice_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        invoice_id INT NOT NULL,
        description TEXT NOT NULL,
        quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
        INDEX idx_invoice_id (invoice_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'contact_messages' => "
    CREATE TABLE IF NOT EXISTS contact_messages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('general', 'support', 'billing', 'feature', 'bug', 'partnership') DEFAULT 'general',
        status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_type (type),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'activity_logs' => "
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT,
        action VARCHAR(50) NOT NULL,
        entity_type VARCHAR(50),
        entity_id INT,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
];

foreach ($requiredTables as $tableName => $sql) {
    try {
        $pdo->exec($sql);
        echo "<div class='result success'>✅ جدول '$tableName' تم إنشاؤه/التحقق منه</div>";
    } catch (PDOException $e) {
        echo "<div class='result error'>❌ خطأ في إنشاء جدول '$tableName': " . $e->getMessage() . "</div>";
    }
}

// الخطوة 5: إنشاء المستخدم التجريبي
echo "<h2>الخطوة 5: إنشاء المستخدم التجريبي</h2>";

try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    
    if ($stmt->fetchColumn() == 0) {
        $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, company_name, subscription_plan) 
            VALUES ('admin', '<EMAIL>', ?, 'المدير', 'العام', 'شركة تجريبية', 'premium')
        ");
        $stmt->execute([$hashedPassword]);
        echo "<div class='result success'>✅ تم إنشاء المستخدم التجريبي</div>";
    } else {
        echo "<div class='result info'>ℹ️ المستخدم التجريبي موجود بالفعل</div>";
    }
    
    // إنشاء عميل تجريبي
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminId = $stmt->fetchColumn();
    
    if ($adminId) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
        $stmt->execute([$adminId]);
        
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("
                INSERT INTO clients (user_id, name, email, phone, company, address, city, country) 
                VALUES (?, 'أحمد محمد', '<EMAIL>', '0501234567', 'شركة العميل', 'شارع الملك فهد', 'الرياض', 'السعودية')
            ");
            $stmt->execute([$adminId]);
            echo "<div class='result success'>✅ تم إنشاء عميل تجريبي</div>";
        }
    }
    
} catch (PDOException $e) {
    echo "<div class='result error'>❌ خطأ في إنشاء البيانات التجريبية: " . $e->getMessage() . "</div>";
}

// الخطوة 6: اختبار الاتصال من التطبيق
echo "<h2>الخطوة 6: اختبار الاتصال من التطبيق</h2>";

try {
    require_once 'config/config.php';
    $testPdo = getDBConnection();
    echo "<div class='result success'>✅ الاتصال من التطبيق نجح</div>";
    
    // اختبار استعلام
    $stmt = $testPdo->query("SELECT COUNT(*) as total FROM users");
    $result = $stmt->fetch();
    echo "<div class='result info'>📊 عدد المستخدمين في قاعدة البيانات: " . $result['total'] . "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال من التطبيق: " . $e->getMessage() . "</div>";
}

// النتيجة النهائية
echo "<h2>النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 تم إصلاح الربط مع قاعدة البيانات بنجاح!</h3>";
echo "<p><strong>✅ قاعدة البيانات جاهزة ومتصلة</strong></p>";
echo "<p><strong>✅ جميع الجداول تم إنشاؤها</strong></p>";
echo "<p><strong>✅ البيانات التجريبية متوفرة</strong></p>";
echo "<p><strong>✅ التطبيق يمكنه الاتصال بقاعدة البيانات</strong></p>";
echo "</div>";

echo "<h3>بيانات الدخول التجريبية:</h3>";
echo "<div class='result info'>";
echo "<div class='code'>";
echo "اسم المستخدم: admin<br>";
echo "كلمة المرور: 123456<br>";
echo "البريد الإلكتروني: <EMAIL>";
echo "</div>";
echo "</div>";

echo "<h3>الخطوات التالية:</h3>";
echo "<div class='result info'>";
echo "<ol>";
echo "<li><a href='index.php' target='_blank'>اختبار الصفحة الرئيسية</a></li>";
echo "<li><a href='auth/login.php' target='_blank'>تسجيل الدخول</a></li>";
echo "<li><a href='contact.php' target='_blank'>اختبار نموذج التواصل</a></li>";
echo "<li><a href='test_complete_system.php' target='_blank'>اختبار النظام الكامل</a></li>";
echo "</ol>";
echo "</div>";

echo "</body></html>";
?>
