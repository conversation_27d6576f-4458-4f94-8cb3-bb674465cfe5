<?php
// ملف إصلاح الأخطاء الشامل
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>إصلاح أخطاء النظام</h1>";

// 1. إصلاح ملف .htaccess
echo "<h2>1. إصلاح ملف .htaccess</h2>";
if (file_exists('.htaccess')) {
    // إعادة تسمية الملف المشكل
    if (rename('.htaccess', '.htaccess.backup')) {
        echo "✅ تم نسخ .htaccess الحالي إلى .htaccess.backup<br>";
    }
}

// إنشاء ملف .htaccess مبسط
$htaccess_content = '# Simple .htaccess for Invoice SaaS
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    Header always append X-Frame-Options SAMEORIGIN
    Header set X-Content-Type-Options nosniff
    Header set X-XSS-Protection "1; mode=block"
</IfModule>

# Hide sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Protect config directory
<Directory "config">
    Order Allow,Deny
    Deny from all
</Directory>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>

# Custom error pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Disable server signature
ServerSignature Off

# PHP settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300

# Session security
php_value session.cookie_httponly 1
php_value session.use_only_cookies 1

# Hide PHP version
php_flag expose_php off
';

file_put_contents('.htaccess', $htaccess_content);
echo "✅ تم إنشاء ملف .htaccess مبسط<br>";

// 2. إصلاح قاعدة البيانات
echo "<h2>2. إصلاح قاعدة البيانات</h2>";
try {
    // الاتصال بـ MySQL
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $dbname = 'invoice_saas';
    
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء/التحقق من قاعدة البيانات<br>";
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء الجداول الأساسية
    $tables = [
        'users' => "
        CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            company_name VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            city VARCHAR(50),
            country VARCHAR(50),
            subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
            subscription_expires_at DATETIME NULL,
            is_active BOOLEAN DEFAULT TRUE,
            email_verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        'clients' => "
        CREATE TABLE IF NOT EXISTS clients (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            company VARCHAR(100),
            address TEXT,
            city VARCHAR(50),
            country VARCHAR(50),
            tax_number VARCHAR(50),
            notes TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        
        'contact_messages' => "
        CREATE TABLE IF NOT EXISTS contact_messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('general', 'support', 'billing', 'feature', 'bug', 'partnership') DEFAULT 'general',
            status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )"
    ];
    
    foreach ($tables as $tableName => $sql) {
        $pdo->exec($sql);
        echo "✅ تم إنشاء/التحقق من جدول $tableName<br>";
    }
    
    // إنشاء مستخدم تجريبي
    $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO users (username, email, password, first_name, last_name, company_name) 
        VALUES ('admin', '<EMAIL>', ?, 'المدير', 'العام', 'شركة تجريبية')
    ");
    $stmt->execute([$hashedPassword]);
    echo "✅ تم إنشاء/التحقق من المستخدم التجريبي<br>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// 3. إنشاء المجلدات المطلوبة
echo "<h2>3. إنشاء المجلدات المطلوبة</h2>";
$directories = [
    'assets/uploads',
    'assets/temp',
    'logs',
    'backups'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ تم إنشاء مجلد $dir<br>";
        } else {
            echo "❌ فشل في إنشاء مجلد $dir<br>";
        }
    } else {
        echo "✅ مجلد $dir موجود<br>";
    }
    
    // إنشاء ملف .gitkeep
    $gitkeep = $dir . '/.gitkeep';
    if (!file_exists($gitkeep)) {
        file_put_contents($gitkeep, '# Keep this directory in git');
    }
}

// 4. إصلاح صلاحيات الملفات
echo "<h2>4. إصلاح صلاحيات الملفات</h2>";
$writableDirs = ['assets/uploads', 'assets/temp', 'logs', 'backups'];
foreach ($writableDirs as $dir) {
    if (is_dir($dir)) {
        chmod($dir, 0755);
        echo "✅ تم تعديل صلاحيات $dir<br>";
    }
}

// 5. اختبار الوظائف الأساسية
echo "<h2>5. اختبار الوظائف الأساسية</h2>";

// اختبار تحميل التكوين
try {
    require_once 'config/config.php';
    echo "✅ تم تحميل ملف التكوين<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل التكوين: " . $e->getMessage() . "<br>";
}

// اختبار الدوال
if (function_exists('sanitize')) {
    echo "✅ دالة sanitize متوفرة<br>";
} else {
    echo "❌ دالة sanitize غير متوفرة<br>";
}

if (function_exists('getDBConnection')) {
    echo "✅ دالة getDBConnection متوفرة<br>";
} else {
    echo "❌ دالة getDBConnection غير متوفرة<br>";
}

echo "<h2>6. تم الانتهاء من الإصلاح!</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li><a href='test.php'>اختبار النظام</a></li>";
echo "<li><a href='contact_simple.php'>اختبار صفحة التواصل المبسطة</a></li>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='contact.php'>صفحة التواصل الأصلية</a></li>";
echo "<li><a href='auth/login.php'>تسجيل الدخول</a> (admin / 123456)</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li>تأكد من تشغيل خدمات Apache و MySQL في XAMPP</li>";
echo "<li>إذا استمرت المشاكل، تحقق من سجل أخطاء Apache</li>";
echo "<li>يمكنك استعادة .htaccess الأصلي من .htaccess.backup إذا لزم الأمر</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    direction: rtl;
    background: #f8f9fa;
}
h1, h2, h3 {
    color: #333;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
ol, ul {
    text-align: right;
}
</style>
