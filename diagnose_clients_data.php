<?php
// تشخيص مشكلة عدم ظهور بيانات العملاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص بيانات العملاء</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-danger { background: #dc3545; }";
echo ".btn-warning { background: #ffc107; color: #212529; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background: #f8f9fa; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔍 تشخيص مشكلة بيانات العملاء</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// الاتصال بقاعدة البيانات
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// الخطوة 1: فحص هيكل الجدول
echo "<h2>1. فحص هيكل جدول العملاء</h2>";
try {
    $stmt = $pdo->query("DESCRIBE clients");
    $columns = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>NULL</th><th>المفتاح</th><th>القيمة الافتراضية</th></tr>";
    $hasUserId = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
        
        if ($column['Field'] == 'user_id') {
            $hasUserId = true;
        }
    }
    echo "</table>";
    
    if ($hasUserId) {
        echo "<div class='result success'>✅ عمود user_id موجود</div>";
    } else {
        echo "<div class='result error'>❌ عمود user_id مفقود</div>";
        echo "<a href='fix_user_id_column.php?action=add_column' class='btn btn-danger'>🔧 إضافة العمود</a>";
        exit;
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</div>";
    exit;
}

// الخطوة 2: فحص البيانات الموجودة
echo "<h2>2. فحص البيانات الموجودة</h2>";
try {
    // إجمالي العملاء
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
    $totalClients = $stmt->fetchColumn();
    echo "<div class='result info'>إجمالي العملاء: $totalClients</div>";
    
    if ($totalClients == 0) {
        echo "<div class='result warning'>⚠️ لا يوجد عملاء في قاعدة البيانات</div>";
        echo "<a href='fix_user_id_column.php?action=diagnose' class='btn btn-warning'>🔧 إضافة عملاء تجريبيين</a>";
    } else {
        // العملاء النشطين
        $stmt = $pdo->query("SELECT COUNT(*) FROM clients WHERE is_active = 1");
        $activeClients = $stmt->fetchColumn();
        echo "<div class='result info'>العملاء النشطين: $activeClients</div>";
        
        // توزيع العملاء على المستخدمين
        $stmt = $pdo->query("SELECT user_id, COUNT(*) as count FROM clients GROUP BY user_id ORDER BY user_id");
        $distribution = $stmt->fetchAll();
        
        echo "<div class='result info'>";
        echo "<strong>توزيع العملاء على المستخدمين:</strong><br>";
        echo "<table>";
        echo "<tr><th>user_id</th><th>عدد العملاء</th></tr>";
        foreach ($distribution as $row) {
            echo "<tr><td>" . $row['user_id'] . "</td><td>" . $row['count'] . "</td></tr>";
        }
        echo "</table>";
        echo "</div>";
        
        // عرض عينة من العملاء
        $stmt = $pdo->query("SELECT id, user_id, name, email, is_active FROM clients ORDER BY id ASC LIMIT 10");
        $sampleClients = $stmt->fetchAll();
        
        echo "<div class='result info'>";
        echo "<strong>عينة من العملاء:</strong><br>";
        echo "<table>";
        echo "<tr><th>ID</th><th>user_id</th><th>الاسم</th><th>البريد</th><th>نشط</th></tr>";
        foreach ($sampleClients as $client) {
            echo "<tr>";
            echo "<td>" . $client['id'] . "</td>";
            echo "<td>" . $client['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars($client['name']) . "</td>";
            echo "<td>" . htmlspecialchars($client['email'] ?? '') . "</td>";
            echo "<td>" . ($client['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص البيانات: " . $e->getMessage() . "</div>";
}

// الخطوة 3: فحص المستخدم الحالي
echo "<h2>3. فحص المستخدم الحالي</h2>";
try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id'])) {
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
        $user = $stmt->fetch();
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            echo "<div class='result info'>تم تسجيل الدخول تلقائياً للمستخدم ID: " . $user['id'] . "</div>";
        } else {
            echo "<div class='result error'>❌ لا يوجد مستخدمين نشطين</div>";
            exit;
        }
    }
    
    $currentUserId = $_SESSION['user_id'];
    echo "<div class='result info'>المستخدم الحالي: ID $currentUserId</div>";
    
    // فحص عملاء المستخدم الحالي
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
    $stmt->execute([$currentUserId]);
    $userClients = $stmt->fetchColumn();
    
    echo "<div class='result info'>عملاء المستخدم الحالي: $userClients</div>";
    
    if ($userClients == 0) {
        echo "<div class='result warning'>⚠️ المستخدم الحالي ليس له عملاء</div>";
        
        if ($totalClients > 0) {
            echo "<div class='result info'>سيتم ربط بعض العملاء بالمستخدم الحالي...</div>";
            
            // ربط أول 3 عملاء بالمستخدم الحالي
            $stmt = $pdo->prepare("UPDATE clients SET user_id = ? LIMIT 3");
            $stmt->execute([$currentUserId]);
            
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
            $stmt->execute([$currentUserId]);
            $newCount = $stmt->fetchColumn();
            
            echo "<div class='result success'>✅ تم ربط $newCount عميل بالمستخدم الحالي</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص المستخدم: " . $e->getMessage() . "</div>";
}

// الخطوة 4: اختبار الاستعلامات
echo "<h2>4. اختبار استعلامات صفحة العملاء</h2>";
try {
    if (isset($currentUserId)) {
        // اختبار استعلام العد
        $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
        $params = [$currentUserId];
        $where_clause = implode(' AND ', $where_conditions);
        
        $countSql = "SELECT COUNT(*) FROM clients c WHERE $where_clause";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $count = $countStmt->fetchColumn();
        
        echo "<div class='result info'>استعلام العد: $count عميل</div>";
        
        if ($count > 0) {
            // اختبار الاستعلام الرئيسي
            $sql = "
                SELECT c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, 
                       c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at,
                       COALESCE(COUNT(i.id), 0) as invoice_count,
                       COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END), 0) as total_paid,
                       COALESCE(SUM(CASE WHEN i.status != 'paid' AND i.status IS NOT NULL THEN i.total_amount ELSE 0 END), 0) as total_pending,
                       MAX(i.created_at) as last_invoice_date
                FROM clients c 
                LEFT JOIN invoices i ON c.id = i.client_id
                WHERE $where_clause 
                GROUP BY c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at
                ORDER BY c.name ASC 
                LIMIT 5
            ";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $clients = $stmt->fetchAll();
            
            echo "<div class='result success'>✅ الاستعلام الرئيسي نجح: " . count($clients) . " عميل</div>";
            
            if (!empty($clients)) {
                echo "<div class='result info'>";
                echo "<strong>العملاء المسترجعة:</strong><br>";
                echo "<table>";
                echo "<tr><th>ID</th><th>الاسم</th><th>البريد</th><th>الشركة</th><th>عدد الفواتير</th></tr>";
                foreach ($clients as $client) {
                    echo "<tr>";
                    echo "<td>" . $client['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($client['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($client['email'] ?? '') . "</td>";
                    echo "<td>" . htmlspecialchars($client['company'] ?? '') . "</td>";
                    echo "<td>" . $client['invoice_count'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "</div>";
                
                echo "<div class='result success'>";
                echo "<h3>🎉 المشكلة محلولة!</h3>";
                echo "<p>البيانات موجودة ويمكن جلبها بنجاح</p>";
                echo "</div>";
            }
        } else {
            echo "<div class='result warning'>⚠️ لا يوجد عملاء نشطين للمستخدم الحالي</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار الاستعلامات: " . $e->getMessage() . "</div>";
    echo "<div class='result info'>تفاصيل الخطأ: " . $e->getTraceAsString() . "</div>";
}

// الخطوة 5: الحلول المقترحة
echo "<h2>5. الحلول المقترحة</h2>";

if ($totalClients == 0) {
    echo "<div class='result warning'>";
    echo "<h3>⚠️ المشكلة: لا يوجد عملاء في قاعدة البيانات</h3>";
    echo "<p><strong>الحل:</strong> إضافة عملاء تجريبيين</p>";
    echo "<a href='fix_user_id_column.php?action=diagnose' class='btn btn-warning'>🔧 إضافة عملاء تجريبيين</a>";
    echo "</div>";
} elseif ($userClients == 0) {
    echo "<div class='result warning'>";
    echo "<h3>⚠️ المشكلة: المستخدم الحالي ليس له عملاء</h3>";
    echo "<p><strong>الحل:</strong> ربط عملاء بالمستخدم الحالي</p>";
    echo "<a href='fix_user_id_column.php?action=diagnose' class='btn btn-warning'>🔧 ربط العملاء</a>";
    echo "</div>";
} elseif ($activeClients == 0) {
    echo "<div class='result warning'>";
    echo "<h3>⚠️ المشكلة: لا يوجد عملاء نشطين</h3>";
    echo "<p><strong>الحل:</strong> تفعيل العملاء الموجودين</p>";
    echo "<a href='?action=activate_clients' class='btn btn-warning'>🔧 تفعيل العملاء</a>";
    echo "</div>";
} else {
    echo "<div class='result success'>";
    echo "<h3>✅ البيانات موجودة ومُهيأة بشكل صحيح</h3>";
    echo "<p>يمكنك الآن اختبار صفحة العملاء</p>";
    echo "</div>";
}

// معالجة تفعيل العملاء
if (isset($_GET['action']) && $_GET['action'] == 'activate_clients') {
    try {
        $pdo->exec("UPDATE clients SET is_active = 1");
        echo "<div class='result success'>✅ تم تفعيل جميع العملاء</div>";
        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في تفعيل العملاء: " . $e->getMessage() . "</div>";
    }
}

echo "<h3>اختبار الصفحات:</h3>";
echo "<a href='clients.php' class='btn btn-success' target='_blank'>🔗 صفحة العملاء</a>";
echo "<a href='dashboard.php' class='btn' target='_blank'>🔗 لوحة التحكم</a>";
echo "<a href='create-invoice.php' class='btn' target='_blank'>🔗 إنشاء فاتورة</a>";

echo "<div class='result info'>";
echo "<strong>بيانات الدخول:</strong><br>";
echo "اسم المستخدم: admin<br>";
echo "كلمة المرور: 123456";
echo "</div>";

echo "</body></html>";
?>
