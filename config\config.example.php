<?php
/**
 * ملف التكوين الأساسي لمنصة SaaS الفواتير
 * نسخ هذا الملف إلى config.php وتحديث الإعدادات
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'invoice_saas');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');

// إعدادات التطبيق
define('APP_NAME', 'منصة الفواتير');
define('APP_URL', 'http://localhost/invoice-saas');
define('APP_VERSION', '1.0.0');
define('APP_ENV', 'development'); // development, production

// إعدادات الأمان
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600); // بالثواني (1 ساعة)
define('CSRF_TOKEN_NAME', 'csrf_token');

// مفاتيح التشفير (يجب تغييرها في الإنتاج)
define('ENCRYPTION_KEY', 'your-32-character-secret-key-here');
define('JWT_SECRET', 'your-jwt-secret-key-here');

// إعدادات البريد الإلكتروني
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-app-password');
define('MAIL_ENCRYPTION', 'tls'); // tls, ssl
define('MAIL_FROM_ADDRESS', '<EMAIL>');
define('MAIL_FROM_NAME', 'منصة الفواتير');

// إعدادات الملفات
define('UPLOAD_PATH', 'assets/uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// إعدادات الدفع (Stripe مثال)
define('STRIPE_PUBLIC_KEY', 'pk_test_your_stripe_public_key');
define('STRIPE_SECRET_KEY', 'sk_test_your_stripe_secret_key');
define('STRIPE_WEBHOOK_SECRET', 'whsec_your_webhook_secret');

// إعدادات PayPal
define('PAYPAL_CLIENT_ID', 'your_paypal_client_id');
define('PAYPAL_CLIENT_SECRET', 'your_paypal_client_secret');
define('PAYPAL_MODE', 'sandbox'); // sandbox, live

// إعدادات التخزين السحابي (AWS S3 مثال)
define('AWS_ACCESS_KEY_ID', 'your_aws_access_key');
define('AWS_SECRET_ACCESS_KEY', 'your_aws_secret_key');
define('AWS_DEFAULT_REGION', 'us-east-1');
define('AWS_BUCKET', 'your-bucket-name');

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_DRIVER', 'file'); // file, redis, memcached
define('CACHE_TTL', 3600); // بالثواني

// إعدادات Redis (إذا كان مستخدماً)
define('REDIS_HOST', '127.0.0.1');
define('REDIS_PORT', 6379);
define('REDIS_PASSWORD', '');
define('REDIS_DATABASE', 0);

// إعدادات API
define('API_RATE_LIMIT', 100); // طلبات في الساعة
define('API_VERSION', 'v1');

// إعدادات التسجيل
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'info'); // debug, info, warning, error
define('LOG_PATH', 'logs/');
define('LOG_MAX_FILES', 30); // عدد ملفات السجل المحفوظة

// إعدادات الإشعارات
define('NOTIFICATIONS_ENABLED', true);
define('PUSH_NOTIFICATIONS_KEY', 'your_push_notifications_key');

// إعدادات التحليلات
define('GOOGLE_ANALYTICS_ID', 'GA_MEASUREMENT_ID');
define('FACEBOOK_PIXEL_ID', 'your_facebook_pixel_id');

// إعدادات الشبكات الاجتماعية
define('FACEBOOK_APP_ID', 'your_facebook_app_id');
define('FACEBOOK_APP_SECRET', 'your_facebook_app_secret');
define('GOOGLE_CLIENT_ID', 'your_google_client_id');
define('GOOGLE_CLIENT_SECRET', 'your_google_client_secret');

// إعدادات الخطط والاشتراكات
define('FREE_PLAN_INVOICE_LIMIT', 5);
define('BASIC_PLAN_INVOICE_LIMIT', 50);
define('PREMIUM_PLAN_INVOICE_LIMIT', -1); // غير محدود

// إعدادات المنطقة الزمنية واللغة
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DEFAULT_LANGUAGE', 'ar');
define('DEFAULT_CURRENCY', 'SAR');

// إعدادات التطوير
if (APP_ENV === 'development') {
    // إظهار الأخطاء في بيئة التطوير
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    
    // تفعيل وضع التطوير
    define('DEBUG_MODE', true);
} else {
    // إخفاء الأخطاء في بيئة الإنتاج
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    
    // تعطيل وضع التطوير
    define('DEBUG_MODE', false);
}

// إعدادات الجلسة
ini_set('session.cookie_lifetime', SESSION_LIFETIME);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // تغيير إلى 1 عند استخدام HTTPS
ini_set('session.use_only_cookies', 1);

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// تعيين الترميز
mb_internal_encoding('UTF-8');

// إعدادات إضافية للأمان
define('BCRYPT_COST', 12);
define('PASSWORD_MIN_LENGTH', 8);
define('LOGIN_ATTEMPTS_LIMIT', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات النسخ الاحتياطي
define('BACKUP_ENABLED', true);
define('BACKUP_PATH', 'backups/');
define('BACKUP_RETENTION_DAYS', 30);

// إعدادات الصيانة
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'الموقع تحت الصيانة، سنعود قريباً');

// إعدادات الأداء
define('ENABLE_GZIP', true);
define('ENABLE_BROWSER_CACHE', true);
define('STATIC_FILES_VERSION', '1.0.0'); // لكسر التخزين المؤقت

// إعدادات المحتوى
define('ITEMS_PER_PAGE', 20);
define('MAX_SEARCH_RESULTS', 100);

// إعدادات الأمان المتقدمة
define('ENABLE_2FA', false); // المصادقة الثنائية
define('ENABLE_IP_WHITELIST', false);
define('ALLOWED_IPS', ['127.0.0.1', '::1']);

// إعدادات التكامل مع خدمات خارجية
define('RECAPTCHA_SITE_KEY', 'your_recaptcha_site_key');
define('RECAPTCHA_SECRET_KEY', 'your_recaptcha_secret_key');

// إعدادات الإحصائيات والتحليل
define('ENABLE_ANALYTICS', true);
define('ENABLE_ERROR_TRACKING', true);

// رسائل النظام الافتراضية
define('DEFAULT_SUCCESS_MESSAGE', 'تم تنفيذ العملية بنجاح');
define('DEFAULT_ERROR_MESSAGE', 'حدث خطأ، يرجى المحاولة مرة أخرى');
define('DEFAULT_WARNING_MESSAGE', 'تحذير: يرجى مراجعة البيانات');

// إعدادات التصدير
define('EXPORT_FORMATS', ['pdf', 'excel', 'csv']);
define('PDF_LIBRARY', 'tcpdf'); // tcpdf, dompdf, mpdf

// إعدادات الإشعارات
define('EMAIL_NOTIFICATIONS', true);
define('SMS_NOTIFICATIONS', false);
define('PUSH_NOTIFICATIONS', false);

// إعدادات الواجهة
define('THEME', 'default');
define('RTL_SUPPORT', true);
define('DARK_MODE_AVAILABLE', true);

// إعدادات API الخارجية
define('CURRENCY_API_KEY', 'your_currency_api_key');
define('SMS_API_KEY', 'your_sms_api_key');

// إعدادات الأرشفة
define('AUTO_ARCHIVE_INVOICES', true);
define('ARCHIVE_AFTER_DAYS', 365);

// إعدادات التدقيق
define('AUDIT_LOG_ENABLED', true);
define('AUDIT_LOG_RETENTION_DAYS', 90);

// إعدادات الشركة الافتراضية
define('COMPANY_NAME', 'شركتك');
define('COMPANY_ADDRESS', 'عنوان شركتك');
define('COMPANY_PHONE', '+966 11 123 4567');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_WEBSITE', 'https://yourcompany.com');

// إعدادات الفواتير الافتراضية
define('DEFAULT_INVOICE_PREFIX', 'INV-');
define('DEFAULT_TAX_RATE', 15);
define('DEFAULT_PAYMENT_TERMS', 'الدفع خلال 30 يوم من تاريخ الفاتورة');
define('DEFAULT_INVOICE_FOOTER', 'شكراً لتعاملكم معنا');

// إعدادات التحقق من التحديثات
define('CHECK_UPDATES', true);
define('UPDATE_SERVER_URL', 'https://updates.yourapp.com');

// إعدادات الدعم الفني
define('SUPPORT_EMAIL', '<EMAIL>');
define('SUPPORT_PHONE', '+966 11 123 4567');
define('SUPPORT_HOURS', 'الأحد - الخميس: 9:00 ص - 6:00 م');

// إعدادات الشروط والأحكام
define('TERMS_VERSION', '1.0');
define('PRIVACY_VERSION', '1.0');
define('TERMS_LAST_UPDATED', '2024-01-01');

// إعدادات التجريب
define('TRIAL_PERIOD_DAYS', 14);
define('TRIAL_INVOICE_LIMIT', 10);

// إعدادات الخصومات والعروض
define('ENABLE_COUPONS', true);
define('REFERRAL_BONUS_ENABLED', true);
define('REFERRAL_BONUS_AMOUNT', 10); // بالدولار

// إعدادات التوطين
define('SUPPORTED_LANGUAGES', ['ar', 'en']);
define('SUPPORTED_CURRENCIES', ['SAR', 'USD', 'EUR']);

// إعدادات الأمان المتقدمة
define('ENABLE_CSRF_PROTECTION', true);
define('ENABLE_XSS_PROTECTION', true);
define('ENABLE_SQL_INJECTION_PROTECTION', true);

// إعدادات الشبكة
define('ENABLE_CDN', false);
define('CDN_URL', 'https://cdn.yourapp.com');

// إعدادات البحث
define('ENABLE_SEARCH', true);
define('SEARCH_ENGINE', 'mysql'); // mysql, elasticsearch

// إعدادات التقارير
define('ENABLE_ADVANCED_REPORTS', true);
define('REPORT_CACHE_TTL', 1800); // 30 دقيقة

// إعدادات الأتمتة
define('ENABLE_AUTO_REMINDERS', true);
define('REMINDER_DAYS_BEFORE_DUE', [7, 3, 1]);
define('REMINDER_DAYS_AFTER_DUE', [1, 7, 14]);

// إعدادات التكامل
define('ENABLE_WEBHOOKS', true);
define('WEBHOOK_TIMEOUT', 30); // ثانية

// إعدادات الجودة
define('ENABLE_RATE_LIMITING', true);
define('ENABLE_REQUEST_LOGGING', true);
define('ENABLE_PERFORMANCE_MONITORING', true);

// إعدادات النهاية
define('CONFIG_LOADED', true);
?>
