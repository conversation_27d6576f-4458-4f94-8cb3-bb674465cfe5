<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (isLoggedIn()) {
    $user_id = $_SESSION['user_id'];
    
    try {
        // تسجيل النشاط
        $pdo = getDBConnection();
        $logStmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
        $logStmt->execute([
            $user_id,
            'logout',
            'تسجيل خروج المستخدم',
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
    } catch (Exception $e) {
        // تجاهل الأخطاء في تسجيل النشاط
    }
    
    // حذف الجلسة
    session_destroy();
    
    // حذف cookie التذكر
    if (isset($_COOKIE['remember_user'])) {
        setcookie('remember_user', '', time() - 3600, '/');
    }
    
    setMessage('تم تسجيل الخروج بنجاح', 'success');
}

redirect(APP_URL . '/index.php');
?>
