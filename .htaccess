# Invoice SaaS - Apache Configuration (Simplified)

# Enable URL Rewriting
RewriteEngine On

# Basic Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN

    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff

    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"
</IfModule>

# Hide sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql)$">
    Require all denied
</FilesMatch>

# Protect config directory
<Directory "config">
    Require all denied
</Directory>

# Protect database directory
<Directory "database">
    Require all denied
</Directory>

# Basic compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>

# Basic PHP settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300

# Session security
php_value session.cookie_httponly 1
php_value session.use_only_cookies 1

# Hide PHP version
php_flag expose_php off

# Custom error pages (optional)
# ErrorDocument 404 /404.php
# ErrorDocument 403 /403.php
# ErrorDocument 500 /500.php
