<?php
// تشخيص الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head><meta charset='UTF-8'><title>تشخيص الأخطاء</title></head>";
echo "<body>";
echo "<h1>🔍 تشخيص الأخطاء</h1>";

// معلومات PHP
echo "<h2>معلومات PHP</h2>";
echo "<p>إصدار PHP: " . phpversion() . "</p>";
echo "<p>الذاكرة المتاحة: " . ini_get('memory_limit') . "</p>";
echo "<p>وقت التنفيذ الأقصى: " . ini_get('max_execution_time') . "</p>";

// فحص الملفات
echo "<h2>فحص الملفات الأساسية</h2>";
$files = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ $file موجود</p>";
    } else {
        echo "<p>❌ $file مفقود</p>";
    }
}

// اختبار تحميل التكوين خطوة بخطوة
echo "<h2>اختبار تحميل التكوين</h2>";

try {
    echo "<p>🔄 محاولة تحميل config/config.php...</p>";
    
    // تحميل الثوابت أولاً
    if (!defined('APP_NAME')) {
        define('APP_NAME', 'Invoice SaaS');
        define('APP_VERSION', '1.0.0');
        define('APP_URL', 'http://localhost/invoice');
        define('DB_HOST', 'localhost');
        define('DB_NAME', 'invoice_saas');
        define('DB_USER', 'root');
        define('DB_PASS', '');
        define('DB_CHARSET', 'utf8mb4');
        echo "<p>✅ تم تعريف الثوابت</p>";
    }
    
    // بدء الجلسة
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
        echo "<p>✅ تم بدء الجلسة</p>";
    }
    
    // تعريف الدوال الأساسية
    if (!function_exists('isLoggedIn')) {
        function isLoggedIn() {
            return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
        }
        echo "<p>✅ تم تعريف دالة isLoggedIn</p>";
    }
    
    if (!function_exists('sanitize')) {
        function sanitize($data) {
            return htmlspecialchars(strip_tags(trim($data)));
        }
        echo "<p>✅ تم تعريف دالة sanitize</p>";
    }
    
    if (!function_exists('redirect')) {
        function redirect($url) {
            if (!headers_sent()) {
                header("Location: " . $url);
                exit();
            } else {
                echo "<script>window.location.href='$url';</script>";
                exit();
            }
        }
        echo "<p>✅ تم تعريف دالة redirect</p>";
    }
    
    if (!function_exists('getDBConnection')) {
        function getDBConnection() {
            try {
                $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
                $pdo = new PDO($dsn, DB_USER, DB_PASS);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                return $pdo;
            } catch (PDOException $e) {
                return null;
            }
        }
        echo "<p>✅ تم تعريف دالة getDBConnection</p>";
    }
    
    echo "<p>✅ جميع الدوال الأساسية جاهزة</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ: " . $e->getMessage() . "</p>";
}

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات</h2>";
try {
    $pdo = getDBConnection();
    if ($pdo) {
        echo "<p>✅ الاتصال بقاعدة البيانات نجح</p>";
    } else {
        echo "<p>⚠️ لا يمكن الاتصال بقاعدة البيانات (هذا طبيعي إذا لم تكن منشأة بعد)</p>";
    }
} catch (Exception $e) {
    echo "<p>⚠️ مشكلة في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار الصفحات
echo "<h2>اختبار الصفحات</h2>";
echo "<p><a href='simple_test.php'>اختبار بسيط</a></p>";
echo "<p><a href='index.php'>الصفحة الرئيسية</a></p>";
echo "<p><a href='contact.php'>صفحة التواصل</a></p>";

echo "<h2>سجل الأخطاء</h2>";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    echo "<p>ملف سجل الأخطاء: $errorLog</p>";
    $errors = file_get_contents($errorLog);
    $recentErrors = array_slice(explode("\n", $errors), -10);
    echo "<pre>" . implode("\n", $recentErrors) . "</pre>";
} else {
    echo "<p>لا يوجد سجل أخطاء متاح</p>";
}

echo "</body></html>";
?>
