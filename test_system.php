<?php
// System Test - Check if everything is working after cleanup
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>System Test</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }";
echo ".test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }";
echo ".success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }";
echo ".error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }";
echo ".warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }";
echo ".test-links { margin: 20px 0; }";
echo ".test-links a { display: inline-block; margin: 5px; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }";
echo ".test-links a:hover { background: #0056b3; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🧪 Invoice SaaS System Test</h1>";

$allGood = true;

// Test 1: PHP Version
echo "<div class='test-result success'>";
echo "✅ PHP Version: " . phpversion();
echo "</div>";

// Test 2: Config Loading
echo "<h2>Configuration Test</h2>";
try {
    require_once 'config/config.php';
    echo "<div class='test-result success'>✅ Configuration loaded successfully</div>";
    
    // Test functions
    if (function_exists('isLoggedIn')) {
        echo "<div class='test-result success'>✅ isLoggedIn() function available</div>";
    } else {
        echo "<div class='test-result error'>❌ isLoggedIn() function missing</div>";
        $allGood = false;
    }
    
    if (function_exists('sanitize')) {
        echo "<div class='test-result success'>✅ sanitize() function available</div>";
    } else {
        echo "<div class='test-result error'>❌ sanitize() function missing</div>";
        $allGood = false;
    }
    
    if (function_exists('redirect')) {
        echo "<div class='test-result success'>✅ redirect() function available</div>";
    } else {
        echo "<div class='test-result error'>❌ redirect() function missing</div>";
        $allGood = false;
    }
    
    if (function_exists('getDBConnection')) {
        echo "<div class='test-result success'>✅ getDBConnection() function available</div>";
    } else {
        echo "<div class='test-result error'>❌ getDBConnection() function missing</div>";
        $allGood = false;
    }
    
} catch (Exception $e) {
    echo "<div class='test-result error'>❌ Configuration error: " . htmlspecialchars($e->getMessage()) . "</div>";
    $allGood = false;
}

// Test 3: Database Connection
echo "<h2>Database Test</h2>";
try {
    $pdo = getDBConnection();
    echo "<div class='test-result success'>✅ Database connection successful</div>";
    
    // Check if tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<div class='test-result success'>✅ Found " . count($tables) . " database tables</div>";
    
} catch (Exception $e) {
    echo "<div class='test-result error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
    $allGood = false;
}

// Test 4: Essential Files
echo "<h2>File Structure Test</h2>";
$essentialFiles = [
    'index.php',
    'contact.php',
    'auth/login.php',
    'config/config.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($essentialFiles as $file) {
    if (file_exists($file)) {
        echo "<div class='test-result success'>✅ $file exists</div>";
    } else {
        echo "<div class='test-result error'>❌ $file missing</div>";
        $allGood = false;
    }
}

// Final Result
echo "<h2>Test Results</h2>";
if ($allGood) {
    echo "<div class='test-result success'>";
    echo "<h3>🎉 All Tests Passed!</h3>";
    echo "<p>The Invoice SaaS system is working correctly. All duplicate function errors have been resolved.</p>";
    echo "</div>";
} else {
    echo "<div class='test-result error'>";
    echo "<h3>⚠️ Some Issues Found</h3>";
    echo "<p>Please review the errors above and fix them before proceeding.</p>";
    echo "</div>";
}

// Test Links
echo "<h2>Test the System</h2>";
echo "<div class='test-links'>";
echo "<a href='index.php' target='_blank'>🏠 Home Page</a>";
echo "<a href='contact.php' target='_blank'>📧 Contact Form</a>";
echo "<a href='auth/login.php' target='_blank'>🔐 Login Page</a>";
echo "<a href='templates.php' target='_blank'>🎨 Templates</a>";
echo "<a href='pricing.php' target='_blank'>💰 Pricing</a>";
echo "<a href='help.php' target='_blank'>❓ Help Center</a>";
echo "</div>";

echo "<h2>System Information</h2>";
echo "<div class='test-result warning'>";
echo "<strong>Server:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "<strong>PHP Version:</strong> " . phpversion() . "<br>";
echo "<strong>Current Directory:</strong> " . getcwd() . "<br>";
echo "<strong>Test Time:</strong> " . date('Y-m-d H:i:s') . "<br>";
echo "</div>";

echo "<h2>Login Credentials</h2>";
echo "<div class='test-result warning'>";
echo "<strong>Demo Account:</strong><br>";
echo "Username: <code>admin</code><br>";
echo "Password: <code>123456</code>";
echo "</div>";

echo "</body></html>";
?>
