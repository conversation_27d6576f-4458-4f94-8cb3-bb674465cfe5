<?php
// تشخيص شامل للمشكلة
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head><meta charset='UTF-8'><title>تشخيص المشكلة</title>";
echo "<style>body{font-family:Arial;margin:20px;} .error{color:red;} .success{color:green;} .warning{color:orange;}</style>";
echo "</head><body>";

echo "<h1>تشخيص مشكلة Internal Server Error</h1>";

// 1. فحص PHP
echo "<h2>1. فحص PHP</h2>";
echo "<p class='success'>✅ PHP يعمل - الإصدار: " . phpversion() . "</p>";

// 2. فحص الملفات المشكوك بها
echo "<h2>2. فحص الملفات</h2>";
$problematic_files = [
    'index.php',
    'contact.php',
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    '.htaccess'
];

foreach ($problematic_files as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $file موجود</p>";
        
        // فحص الأخطاء النحوية
        $output = [];
        $return_var = 0;
        exec("php -l \"$file\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<p class='success'>✅ $file - لا توجد أخطاء نحوية</p>";
        } else {
            echo "<p class='error'>❌ $file - خطأ نحوي: " . implode(' ', $output) . "</p>";
        }
    } else {
        echo "<p class='error'>❌ $file غير موجود</p>";
    }
}

// 3. فحص سجل أخطاء Apache
echo "<h2>3. فحص سجل الأخطاء</h2>";
$error_logs = [
    'C:/xampp/apache/logs/error.log',
    'C:/wamp64/logs/apache_error.log',
    '/var/log/apache2/error.log',
    'logs/error.log'
];

$found_log = false;
foreach ($error_logs as $log_file) {
    if (file_exists($log_file)) {
        $found_log = true;
        echo "<p class='success'>✅ وجد سجل الأخطاء: $log_file</p>";
        
        // قراءة آخر 10 أسطر
        $lines = file($log_file);
        $last_lines = array_slice($lines, -10);
        
        echo "<h4>آخر 10 أخطاء:</h4>";
        echo "<pre style='background:#f5f5f5;padding:10px;border-radius:5px;'>";
        foreach ($last_lines as $line) {
            if (strpos($line, 'invoice') !== false || strpos($line, 'contact') !== false) {
                echo "<span style='color:red;'>" . htmlspecialchars($line) . "</span>";
            } else {
                echo htmlspecialchars($line);
            }
        }
        echo "</pre>";
        break;
    }
}

if (!$found_log) {
    echo "<p class='warning'>⚠️ لم يتم العثور على سجل أخطاء Apache</p>";
}

// 4. اختبار تحميل الملفات واحداً تلو الآخر
echo "<h2>4. اختبار تحميل الملفات</h2>";

// اختبار config.php
echo "<h3>اختبار config/config.php:</h3>";
try {
    ob_start();
    include 'config/config.php';
    $config_output = ob_get_clean();
    echo "<p class='success'>✅ config.php تم تحميله بنجاح</p>";
    if (!empty($config_output)) {
        echo "<p class='warning'>⚠️ config.php ينتج مخرجات: " . htmlspecialchars($config_output) . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في config.php: " . $e->getMessage() . "</p>";
} catch (ParseError $e) {
    echo "<p class='error'>❌ خطأ نحوي في config.php: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p class='error'>❌ خطأ فادح في config.php: " . $e->getMessage() . "</p>";
}

// اختبار functions.php
echo "<h3>اختبار includes/functions.php:</h3>";
try {
    ob_start();
    include 'includes/functions.php';
    $functions_output = ob_get_clean();
    echo "<p class='success'>✅ functions.php تم تحميله بنجاح</p>";
    if (!empty($functions_output)) {
        echo "<p class='warning'>⚠️ functions.php ينتج مخرجات: " . htmlspecialchars($functions_output) . "</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في functions.php: " . $e->getMessage() . "</p>";
} catch (ParseError $e) {
    echo "<p class='error'>❌ خطأ نحوي في functions.php: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p class='error'>❌ خطأ فادح في functions.php: " . $e->getMessage() . "</p>";
}

// 5. اختبار قاعدة البيانات
echo "<h2>5. اختبار قاعدة البيانات</h2>";
try {
    $pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '');
    echo "<p class='success'>✅ الاتصال بـ MySQL نجح</p>";
    
    // التحقق من قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE 'invoice_saas'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ قاعدة البيانات invoice_saas موجودة</p>";
    } else {
        echo "<p class='warning'>⚠️ قاعدة البيانات invoice_saas غير موجودة</p>";
    }
} catch (PDOException $e) {
    echo "<p class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// 6. فحص .htaccess
echo "<h2>6. فحص .htaccess</h2>";
if (file_exists('.htaccess')) {
    $htaccess_content = file_get_contents('.htaccess');
    echo "<p class='success'>✅ ملف .htaccess موجود</p>";
    echo "<h4>محتوى .htaccess:</h4>";
    echo "<pre style='background:#f5f5f5;padding:10px;border-radius:5px;'>";
    echo htmlspecialchars($htaccess_content);
    echo "</pre>";
} else {
    echo "<p class='warning'>⚠️ ملف .htaccess غير موجود</p>";
}

// 7. معلومات الخادم
echo "<h2>7. معلومات الخادم</h2>";
echo "<p>خادم الويب: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف') . "</p>";
echo "<p>المجلد الحالي: " . getcwd() . "</p>";
echo "<p>مسار الطلب: " . ($_SERVER['REQUEST_URI'] ?? 'غير معروف') . "</p>";

// 8. الحلول المقترحة
echo "<h2>8. الحلول المقترحة</h2>";
echo "<ol>";
echo "<li><a href='simple_test.php'>اختبار بسيط</a> - للتأكد من عمل PHP</li>";
echo "<li><a href='index_basic.php'>صفحة رئيسية بسيطة</a> - بدون تعقيدات</li>";
echo "<li><a href='contact_basic.php'>صفحة تواصل بسيطة</a> - بدون قاعدة بيانات</li>";
echo "<li>حذف ملف .htaccess مؤقتاً</li>";
echo "<li>فحص سجل أخطاء Apache</li>";
echo "</ol>";

echo "</body></html>";
?>
