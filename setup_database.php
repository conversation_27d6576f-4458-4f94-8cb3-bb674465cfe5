<?php
// إعداد قاعدة البيانات والجداول
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد قاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 إعداد قاعدة البيانات</h1>";

try {
    // الاتصال بـ MySQL
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $dbname = 'invoice_saas';
    
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='result success'>✅ الاتصال بـ MySQL نجح</div>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='result success'>✅ تم إنشاء قاعدة البيانات: $dbname</div>";
    
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء جدول المستخدمين
    $sql = "
    CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        company_name VARCHAR(100),
        phone VARCHAR(20),
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50),
        subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
        subscription_expires_at DATETIME NULL,
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='result success'>✅ تم إنشاء جدول المستخدمين</div>";
    
    // إنشاء جدول العملاء
    $sql = "
    CREATE TABLE IF NOT EXISTS clients (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        company VARCHAR(100),
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50),
        tax_number VARCHAR(50),
        notes TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='result success'>✅ تم إنشاء جدول العملاء</div>";
    
    // إنشاء جدول الفواتير
    $sql = "
    CREATE TABLE IF NOT EXISTS invoices (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        client_id INT NOT NULL,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        title VARCHAR(255),
        issue_date DATE NOT NULL,
        due_date DATE,
        status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
        subtotal DECIMAL(10,2) DEFAULT 0,
        tax_rate DECIMAL(5,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'SAR',
        notes TEXT,
        terms TEXT,
        template_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='result success'>✅ تم إنشاء جدول الفواتير</div>";
    
    // إنشاء جدول عناصر الفواتير
    $sql = "
    CREATE TABLE IF NOT EXISTS invoice_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        invoice_id INT NOT NULL,
        description TEXT NOT NULL,
        quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='result success'>✅ تم إنشاء جدول عناصر الفواتير</div>";
    
    // إنشاء جدول رسائل التواصل
    $sql = "
    CREATE TABLE IF NOT EXISTS contact_messages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('general', 'support', 'billing', 'feature', 'bug', 'partnership') DEFAULT 'general',
        status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='result success'>✅ تم إنشاء جدول رسائل التواصل</div>";
    
    // إنشاء جدول سجل الأنشطة
    $sql = "
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT,
        action VARCHAR(50) NOT NULL,
        entity_type VARCHAR(50),
        entity_id INT,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='result success'>✅ تم إنشاء جدول سجل الأنشطة</div>";
    
    // إنشاء مستخدم تجريبي
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    
    if ($stmt->fetchColumn() == 0) {
        $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, company_name) 
            VALUES ('admin', '<EMAIL>', ?, 'المدير', 'العام', 'شركة تجريبية')
        ");
        $stmt->execute([$hashedPassword]);
        echo "<div class='result success'>✅ تم إنشاء المستخدم التجريبي</div>";
        echo "<div class='result info'><strong>بيانات الدخول:</strong><br>اسم المستخدم: admin<br>كلمة المرور: 123456</div>";
    } else {
        echo "<div class='result info'>✅ المستخدم التجريبي موجود بالفعل</div>";
    }
    
    // إنشاء عميل تجريبي
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminId = $stmt->fetchColumn();
    
    if ($adminId) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
        $stmt->execute([$adminId]);
        
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("
                INSERT INTO clients (user_id, name, email, phone, company, address, city, country) 
                VALUES (?, 'أحمد محمد', '<EMAIL>', '0501234567', 'شركة العميل', 'شارع الملك فهد', 'الرياض', 'السعودية')
            ");
            $stmt->execute([$adminId]);
            echo "<div class='result success'>✅ تم إنشاء عميل تجريبي</div>";
        }
    }
    
    echo "<div class='result success'>";
    echo "<h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام النظام بالكامل</p>";
    echo "</div>";
    
    echo "<h3>الخطوات التالية:</h3>";
    echo "<p><a href='auth/login.php'>تسجيل الدخول</a> باستخدام: admin / 123456</p>";
    echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
    
} catch (PDOException $e) {
    echo "<div class='result error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
    echo "<div class='result info'>";
    echo "<h4>تأكد من:</h4>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL في XAMPP</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</body></html>";
?>
