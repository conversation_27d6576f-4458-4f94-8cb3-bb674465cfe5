<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف العميل غير صحيح']);
    exit;
}

$user = getCurrentUser();
$user_id = $user['id'];
$client_id = (int)$_GET['id'];

try {
    $pdo = getDBConnection();
    
    // جلب بيانات العميل
    $stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ? AND user_id = ? AND is_active = 1");
    $stmt->execute([$client_id, $user_id]);
    $client = $stmt->fetch();
    
    if (!$client) {
        echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'client' => $client
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
