<?php
// إصلاح جدول العملاء وإضافة العمود المفقود
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح جدول العملاء</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 إصلاح جدول العملاء</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// الاتصال بقاعدة البيانات
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// فحص هيكل جدول العملاء
echo "<h2>1. فحص هيكل جدول العملاء</h2>";
try {
    $stmt = $pdo->query("DESCRIBE clients");
    $columns = $stmt->fetchAll();
    
    echo "<div class='result info'>";
    echo "<strong>أعمدة جدول العملاء الحالية:</strong><br>";
    $hasUserId = false;
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
        if ($column['Field'] == 'user_id') {
            $hasUserId = true;
        }
    }
    echo "</div>";
    
    if ($hasUserId) {
        echo "<div class='result success'>✅ عمود user_id موجود</div>";
    } else {
        echo "<div class='result warning'>⚠️ عمود user_id مفقود - سيتم إضافته</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</div>";
    
    // إذا كان الجدول غير موجود، أنشئه
    echo "<div class='result warning'>⚠️ جدول العملاء غير موجود - سيتم إنشاؤه</div>";
    
    try {
        $createTableSql = "
        CREATE TABLE clients (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            company VARCHAR(100),
            address TEXT,
            city VARCHAR(50),
            country VARCHAR(50),
            tax_number VARCHAR(50),
            notes TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTableSql);
        echo "<div class='result success'>✅ تم إنشاء جدول العملاء بنجاح</div>";
        $hasUserId = true;
        
    } catch (Exception $e2) {
        echo "<div class='result error'>❌ خطأ في إنشاء الجدول: " . $e2->getMessage() . "</div>";
        exit;
    }
}

// إضافة عمود user_id إذا كان مفقوداً
if (!$hasUserId) {
    echo "<h2>2. إضافة عمود user_id</h2>";
    try {
        // إضافة العمود
        $pdo->exec("ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
        echo "<div class='result success'>✅ تم إضافة عمود user_id</div>";
        
        // إضافة الفهرس
        $pdo->exec("ALTER TABLE clients ADD INDEX idx_user_id (user_id)");
        echo "<div class='result success'>✅ تم إضافة فهرس user_id</div>";
        
        // محاولة إضافة المفتاح الخارجي (قد يفشل إذا كانت هناك بيانات غير متوافقة)
        try {
            $pdo->exec("ALTER TABLE clients ADD FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE");
            echo "<div class='result success'>✅ تم إضافة المفتاح الخارجي</div>";
        } catch (Exception $e) {
            echo "<div class='result warning'>⚠️ لم يتم إضافة المفتاح الخارجي: " . $e->getMessage() . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في إضافة عمود user_id: " . $e->getMessage() . "</div>";
    }
}

// تحديث البيانات الموجودة
echo "<h2>3. تحديث البيانات الموجودة</h2>";
try {
    // فحص إذا كانت هناك بيانات بدون user_id
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients WHERE user_id IS NULL OR user_id = 0");
    $nullUserIdCount = $stmt->fetchColumn();
    
    if ($nullUserIdCount > 0) {
        echo "<div class='result warning'>⚠️ يوجد $nullUserIdCount سجل بدون user_id</div>";
        
        // البحث عن أول مستخدم نشط
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
        $firstUser = $stmt->fetch();
        
        if ($firstUser) {
            $userId = $firstUser['id'];
            $stmt = $pdo->prepare("UPDATE clients SET user_id = ? WHERE user_id IS NULL OR user_id = 0");
            $stmt->execute([$userId]);
            
            echo "<div class='result success'>✅ تم تحديث السجلات لتنتمي للمستخدم ID: $userId</div>";
        } else {
            echo "<div class='result error'>❌ لا يوجد مستخدمين نشطين لربط العملاء بهم</div>";
        }
    } else {
        echo "<div class='result success'>✅ جميع السجلات لها user_id صحيح</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في تحديث البيانات: " . $e->getMessage() . "</div>";
}

// إضافة بيانات تجريبية
echo "<h2>4. إضافة بيانات تجريبية</h2>";
try {
    // البحث عن مستخدم للربط
    $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
    $user = $stmt->fetch();
    
    if ($user) {
        $userId = $user['id'];
        
        // فحص إذا كان هناك عملاء للمستخدم
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
        $stmt->execute([$userId]);
        $clientCount = $stmt->fetchColumn();
        
        if ($clientCount == 0) {
            echo "<div class='result warning'>⚠️ لا يوجد عملاء للمستخدم - سيتم إضافة عملاء تجريبيين</div>";
            
            $sampleClients = [
                ['أحمد محمد', '<EMAIL>', '0501234567', 'شركة التقنية المتقدمة'],
                ['فاطمة علي', '<EMAIL>', '0509876543', 'مؤسسة الإبداع'],
                ['محمد سالم', '<EMAIL>', '0551234567', 'شركة الحلول الذكية']
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO clients (user_id, name, email, phone, company, is_active)
                VALUES (?, ?, ?, ?, ?, 1)
            ");
            
            $added = 0;
            foreach ($sampleClients as $client) {
                try {
                    $stmt->execute(array_merge([$userId], $client));
                    $added++;
                } catch (Exception $e) {
                    // تجاهل الأخطاء (قد يكون العميل موجود)
                }
            }
            
            echo "<div class='result success'>✅ تم إضافة $added عميل تجريبي</div>";
        } else {
            echo "<div class='result info'>ℹ️ يوجد $clientCount عميل للمستخدم</div>";
        }
    } else {
        echo "<div class='result error'>❌ لا يوجد مستخدمين لربط العملاء بهم</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في إضافة البيانات التجريبية: " . $e->getMessage() . "</div>";
}

// اختبار الاستعلام المُصحح
echo "<h2>5. اختبار الاستعلام المُصحح</h2>";
try {
    // محاكاة تسجيل الدخول
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id'])) {
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
        $user = $stmt->fetch();
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
        }
    }
    
    if (isset($_SESSION['user_id'])) {
        $testUserId = $_SESSION['user_id'];
        
        // اختبار الاستعلام البسيط
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ? AND is_active = 1");
        $stmt->execute([$testUserId]);
        $count = $stmt->fetchColumn();
        
        echo "<div class='result success'>✅ الاستعلام البسيط نجح: $count عميل</div>";
        
        // اختبار الاستعلام المعقد
        $sql = "
            SELECT c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, 
                   c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at,
                   COALESCE(COUNT(i.id), 0) as invoice_count,
                   COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END), 0) as total_paid,
                   COALESCE(SUM(CASE WHEN i.status != 'paid' AND i.status IS NOT NULL THEN i.total_amount ELSE 0 END), 0) as total_pending,
                   MAX(i.created_at) as last_invoice_date
            FROM clients c 
            LEFT JOIN invoices i ON c.id = i.client_id
            WHERE c.user_id = ? AND c.is_active = 1
            GROUP BY c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at
            ORDER BY c.name ASC 
            LIMIT 20 OFFSET 0
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$testUserId]);
        $clients = $stmt->fetchAll();
        
        echo "<div class='result success'>✅ الاستعلام المعقد نجح: " . count($clients) . " عميل</div>";
        
        if (!empty($clients)) {
            echo "<div class='result info'>";
            echo "<strong>عينة من العملاء:</strong><br>";
            foreach (array_slice($clients, 0, 3) as $client) {
                echo "- " . htmlspecialchars($client['name']) . " (" . htmlspecialchars($client['email'] ?? 'بدون بريد') . ")<br>";
            }
            echo "</div>";
        }
        
    } else {
        echo "<div class='result warning'>⚠️ لا يمكن اختبار الاستعلام - لا يوجد مستخدم مسجل دخوله</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار الاستعلام: " . $e->getMessage() . "</div>";
}

echo "<h2>6. النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 تم إصلاح جدول العملاء!</h3>";
echo "<p><strong>ما تم إصلاحه:</strong></p>";
echo "<ul>";
echo "<li>✅ إضافة عمود user_id إلى جدول العملاء</li>";
echo "<li>✅ إضافة فهرس للأداء</li>";
echo "<li>✅ ربط العملاء الموجودين بالمستخدمين</li>";
echo "<li>✅ إضافة بيانات تجريبية</li>";
echo "<li>✅ اختبار الاستعلامات</li>";
echo "</ul>";
echo "</div>";

echo "<h3>اختبار الصفحة المُصححة:</h3>";
echo "<a href='clients.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔗 صفحة العملاء</a>";
echo "<a href='dashboard.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔗 لوحة التحكم</a>";

echo "</body></html>";
?>
