<?php
// اختبار الإصلاح
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار الإصلاح</h1>";

// 1. اختبار تحميل التكوين
echo "<h2>1. اختبار التكوين</h2>";
try {
    require_once 'config/config.php';
    echo "<p style='color:green;'>✅ تم تحميل التكوين بنجاح</p>";
    
    // اختبار الدوال
    if (function_exists('isLoggedIn')) {
        echo "<p style='color:green;'>✅ دالة isLoggedIn متوفرة</p>";
    } else {
        echo "<p style='color:red;'>❌ دالة isLoggedIn غير متوفرة</p>";
    }
    
    if (function_exists('sanitize')) {
        echo "<p style='color:green;'>✅ دالة sanitize متوفرة</p>";
    } else {
        echo "<p style='color:red;'>❌ دالة sanitize غير متوفرة</p>";
    }
    
    if (function_exists('getDBConnection')) {
        echo "<p style='color:green;'>✅ دالة getDBConnection متوفرة</p>";
    } else {
        echo "<p style='color:red;'>❌ دالة getDBConnection غير متوفرة</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color:red;'>❌ خطأ في التكوين: " . $e->getMessage() . "</p>";
}

// 2. اختبار الصفحات
echo "<h2>2. اختبار الصفحات</h2>";
echo "<p><a href='index.php' target='_blank'>اختبار الصفحة الرئيسية</a></p>";
echo "<p><a href='contact.php' target='_blank'>اختبار صفحة التواصل</a></p>";
echo "<p><a href='auth/login.php' target='_blank'>اختبار تسجيل الدخول</a></p>";

// 3. اختبار قاعدة البيانات
echo "<h2>3. اختبار قاعدة البيانات</h2>";
try {
    $pdo = getDBConnection();
    echo "<p style='color:green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
} catch (Exception $e) {
    echo "<p style='color:red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>4. النتيجة</h2>";
echo "<div style='background:#d4edda;padding:15px;border-radius:5px;'>";
echo "<h3>🎉 تم حل مشكلة التضارب في الدوال!</h3>";
echo "<p>يمكنك الآن اختبار الصفحات بأمان.</p>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
h1, h2 {
    color: #333;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
