# منصة SaaS لإنشاء الفواتير

منصة متكاملة لإنشاء وإدارة الفواتير مبنية بـ PHP مع قاعدة بيانات MySQL. تتيح للمستخدمين إنشاء فواتير احترافية بسهولة مع إدارة العملاء والتقارير المفصلة.

## المميزات الرئيسية

### 🧾 إدارة الفواتير
- إنشاء فواتير احترافية بقوالب متنوعة
- معاينة مباشرة للفواتير
- تصدير PDF وطباعة
- تتبع حالة الفواتير (مسودة، مرسلة، مدفوعة، متأخرة)
- إرسال الفواتير بالبريد الإلكتروني

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ التعاملات
- إحصائيات العملاء والمدفوعات

### 📊 التقارير والإحصائيات
- تقارير الإيرادات الشهرية والسنوية
- رسوم بيانية تفاعلية
- تحليل أداء المبيعات
- تقارير الفواتير المتأخرة

### 🎨 قوالب متنوعة
- قوالب كلاسيكية وحديثة
- تخصيص كامل للتصميم
- معاينة مباشرة للقوالب

### ⚙️ إعدادات متقدمة
- إدارة الملف الشخصي
- إعدادات الفواتير الافتراضية
- إدارة الاشتراكات
- نظام الإشعارات

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مكتبات PHP المطلوبة:
  - PDO MySQL
  - mbstring
  - json
  - session

## التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/invoice-saas.git
cd invoice-saas
```

### 2. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p < database/invoice_saas.sql
```

### 3. إعداد ملف التكوين
```bash
# نسخ ملف التكوين
cp config/config.example.php config/config.php

# تحرير إعدادات قاعدة البيانات
nano config/config.php
```

### 4. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلدات معينة
chmod 755 assets/uploads/
chmod 755 assets/temp/
```

### 5. إعداد خادم الويب

#### Apache
```apache
<VirtualHost *:80>
    DocumentRoot /path/to/invoice-saas
    ServerName invoice-saas.local
    
    <Directory /path/to/invoice-saas>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name invoice-saas.local;
    root /path/to/invoice-saas;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## الاستخدام

### إنشاء حساب جديد
1. اذهب إلى الصفحة الرئيسية
2. انقر على "إنشاء حساب"
3. املأ البيانات المطلوبة
4. ابدأ بإنشاء فواتيرك

### إنشاء فاتورة جديدة
1. سجل دخولك إلى لوحة التحكم
2. انقر على "إنشاء فاتورة جديدة"
3. اختر القالب المناسب
4. أضف بيانات العميل والعناصر
5. احفظ أو أرسل الفاتورة

### إدارة العملاء
1. اذهب إلى صفحة "العملاء"
2. أضف عملاء جدد أو حرر الموجودين
3. تتبع إحصائيات كل عميل

## هيكل المشروع

```
invoice-saas/
├── assets/                 # الملفات الثابتة (CSS, JS, Images)
│   ├── css/
│   ├── js/
│   └── images/
├── auth/                   # صفحات المصادقة
│   ├── login.php
│   ├── register.php
│   └── logout.php
├── config/                 # ملفات التكوين
│   └── config.php
├── database/               # قاعدة البيانات
│   └── invoice_saas.sql
├── includes/               # الملفات المشتركة
│   ├── header.php
│   ├── footer.php
│   └── functions.php
├── ajax/                   # ملفات AJAX
├── dashboard.php           # لوحة التحكم
├── invoices.php           # إدارة الفواتير
├── clients.php            # إدارة العملاء
├── reports.php            # التقارير
├── settings.php           # الإعدادات
├── templates.php          # معرض القوالب
├── create-invoice.php     # إنشاء فاتورة
├── pricing.php            # خطط الأسعار
├── help.php              # مركز المساعدة
├── contact.php           # صفحة التواصل
└── index.php             # الصفحة الرئيسية
```

## خطط الاشتراك

### المجانية
- 5 فواتير شهرياً
- 3 قوالب أساسية
- إدارة العملاء الأساسية

### الأساسية ($19/شهر)
- 50 فاتورة شهرياً
- جميع القوالب
- التقارير الأساسية
- الدعم عبر البريد

### المتقدمة ($49/شهر)
- فواتير غير محدودة
- قوالب مميزة
- التقارير المتقدمة
- API كامل
- الدعم المتقدم

## الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات SQL Injection
- حماية من هجمات XSS
- جلسات آمنة
- تسجيل الأنشطة

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

- 📧 البريد الإلكتروني: <EMAIL>
- 📞 الهاتف: +966 11 123 4567
- 💬 الدردشة المباشرة: متوفرة في الموقع

## التحديثات

### الإصدار 1.0.0
- إطلاق المنصة الأساسية
- إدارة الفواتير والعملاء
- التقارير الأساسية
- 5 قوالب احترافية

### قادماً في الإصدار 1.1.0
- تطبيق الجوال
- تكامل مع بوابات الدفع
- إشعارات تلقائية
- قوالب إضافية

---

تم تطوير هذا المشروع بـ ❤️ لمساعدة الشركات في إدارة فواتيرها بكفاءة أكبر.
