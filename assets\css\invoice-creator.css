/* منشئ الفواتير - تنسيقات خاصة */

.template-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.template-option {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.template-option:hover {
    border-color: #007bff;
    transform: translateY(-2px);
}

.template-option.selected {
    border-color: #28a745;
    background-color: #f8fff9;
}

.template-preview-mini {
    height: 80px;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 8px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-preview-mini img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-placeholder-mini {
    color: #6c757d;
    font-size: 1.5rem;
}

.template-name {
    font-size: 0.85rem;
    font-weight: 500;
    color: #333;
}

/* عناصر الفاتورة */
.invoice-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.invoice-item:hover {
    background: #f1f3f4;
}

.remove-invoice-item {
    width: 100%;
    height: 38px;
}

/* ملخص المجاميع */
.totals-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.total-row:last-child {
    border-bottom: none;
}

.final-total {
    font-weight: bold;
    font-size: 1.1rem;
    background: #007bff;
    color: white;
    margin: 10px -20px -20px;
    padding: 15px 20px;
    border-radius: 0 0 10px 10px;
}

/* معاينة الفاتورة */
.invoice-preview {
    background: white;
    border-radius: 10px;
    padding: 20px;
    min-height: 400px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.invoice-preview .invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.invoice-preview .invoice-title {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
}

.invoice-preview .invoice-info {
    text-align: left;
}

.invoice-preview .invoice-parties {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

.invoice-preview .party-info h6 {
    color: #007bff;
    margin-bottom: 10px;
    font-weight: 600;
}

.invoice-preview .items-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.invoice-preview .items-table th,
.invoice-preview .items-table td {
    padding: 12px;
    text-align: right;
    border: 1px solid #ddd;
}

.invoice-preview .items-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.invoice-preview .invoice-totals {
    text-align: left;
    margin-top: 20px;
}

.invoice-preview .invoice-totals .total-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.invoice-preview .invoice-totals .final-total {
    font-size: 1.2rem;
    font-weight: bold;
    border-top: 2px solid #007bff;
    padding-top: 15px;
    margin-top: 15px;
    background: none;
    color: #333;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .template-selector {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .invoice-item .row {
        --bs-gutter-x: 0.5rem;
    }
    
    .invoice-item .col-md-5,
    .invoice-item .col-md-2,
    .invoice-item .col-md-1 {
        margin-bottom: 10px;
    }
    
    .totals-summary {
        margin-top: 20px;
    }
    
    .invoice-preview .invoice-header {
        flex-direction: column;
        text-align: center;
    }
    
    .invoice-preview .invoice-parties {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .invoice-preview .items-table {
        font-size: 0.85rem;
    }
}

/* تأثيرات الحركة */
.invoice-item {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات إضافية */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

/* تنسيق خاص للمودال */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* تحسينات للطباعة */
@media print {
    .invoice-preview {
        box-shadow: none;
        border: none;
    }
    
    .card,
    .btn,
    .form-control {
        display: none !important;
    }
    
    .invoice-preview {
        display: block !important;
    }
}

/* تنسيق خاص للحقول المطلوبة */
.form-label::after {
    content: "";
}

.form-label[data-required="true"]::after {
    content: " *";
    color: #dc3545;
}

/* تحسين مظهر الجداول */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* تحسين أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-buttons .btn {
    flex: 1;
    min-width: 120px;
}

@media (max-width: 576px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
}

/* تحسين مظهر البطاقات */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

/* تحسين مظهر النماذج */
.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

/* تحسين مظهر التبديل */
.nav-tabs .nav-link {
    border-radius: 10px 10px 0 0;
    border: none;
    background: #f8f9fa;
    color: #6c757d;
    margin-left: 5px;
}

.nav-tabs .nav-link.active {
    background: #007bff;
    color: white;
}

.nav-tabs .nav-link:hover {
    background: #e9ecef;
    color: #495057;
}

.nav-tabs .nav-link.active:hover {
    background: #0056b3;
    color: white;
}
