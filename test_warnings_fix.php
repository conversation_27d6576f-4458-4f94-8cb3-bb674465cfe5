<?php
// اختبار إصلاح التحذيرات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار إصلاح التحذيرات</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo ".btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 اختبار إصلاح التحذيرات</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين بدون أخطاء</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// اختبار دالة sanitize مع قيم مختلفة
echo "<h2>1. اختبار دالة sanitize()</h2>";

$testValues = [
    null => 'null',
    '' => 'سلسلة فارغة',
    '  test  ' => 'نص مع مسافات',
    '<script>alert("test")</script>' => 'نص مع HTML',
    'نص عربي' => 'نص عربي',
    123 => 'رقم',
    0 => 'صفر'
];

foreach ($testValues as $value => $description) {
    try {
        $result = sanitize($value);
        echo "<div class='result success'>";
        echo "<strong>$description:</strong> '" . var_export($value, true) . "' → '" . $result . "'";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div class='result error'>";
        echo "<strong>$description:</strong> خطأ - " . $e->getMessage();
        echo "</div>";
    }
}

// اختبار دوال التنسيق الأخرى
echo "<h2>2. اختبار دوال التنسيق الأخرى</h2>";

try {
    echo "<div class='result success'>formatCurrency(null): " . formatCurrency(null) . "</div>";
    echo "<div class='result success'>formatCurrency(''): " . formatCurrency('') . "</div>";
    echo "<div class='result success'>formatCurrency(1500.75): " . formatCurrency(1500.75) . "</div>";
} catch (Exception $e) {
    echo "<div class='result error'>خطأ في formatCurrency: " . $e->getMessage() . "</div>";
}

try {
    echo "<div class='result success'>formatDate(null): '" . formatDate(null) . "'</div>";
    echo "<div class='result success'>formatDate(''): '" . formatDate('') . "'</div>";
    echo "<div class='result success'>formatDate('2024-01-15'): '" . formatDate('2024-01-15', 'd/m/Y') . "'</div>";
} catch (Exception $e) {
    echo "<div class='result error'>خطأ في formatDate: " . $e->getMessage() . "</div>";
}

// اختبار محاكاة POST data لصفحة إنشاء الفواتير
echo "<h2>3. اختبار معالجة بيانات POST</h2>";

// محاكاة بيانات POST مفقودة
$_POST = [
    'client_id' => '1',
    'invoice_number' => 'INV-2024-001',
    // title مفقود عمداً
    'issue_date' => '2024-01-15',
    // due_date مفقود عمداً
    'notes' => 'ملاحظات الفاتورة',
    // terms مفقود عمداً
    'template_id' => '1'
];

try {
    // محاكاة نفس الكود من create-invoice.php
    $client_id = (int)($_POST['client_id'] ?? 0);
    $invoice_number = sanitize($_POST['invoice_number'] ?? '');
    $title = sanitize($_POST['title'] ?? '');
    $issue_date = sanitize($_POST['issue_date'] ?? '');
    $due_date = sanitize($_POST['due_date'] ?? '');
    $notes = sanitize($_POST['notes'] ?? '');
    $terms = sanitize($_POST['terms'] ?? '');
    $template_id = (int)($_POST['template_id'] ?? 1);
    
    echo "<div class='result success'>✅ معالجة بيانات POST نجحت بدون تحذيرات</div>";
    echo "<div class='result info'>";
    echo "client_id: $client_id<br>";
    echo "invoice_number: '$invoice_number'<br>";
    echo "title: '$title'<br>";
    echo "issue_date: '$issue_date'<br>";
    echo "due_date: '$due_date'<br>";
    echo "notes: '$notes'<br>";
    echo "terms: '$terms'<br>";
    echo "template_id: $template_id<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في معالجة POST: " . $e->getMessage() . "</div>";
}

// اختبار معالجة عناصر الفاتورة
echo "<h2>4. اختبار معالجة عناصر الفاتورة</h2>";

$_POST['items'] = [
    [
        'description' => 'خدمة تطوير',
        'quantity' => '2',
        'unit_price' => '500'
    ],
    [
        'description' => 'خدمة استشارة',
        // quantity مفقود عمداً
        'unit_price' => '300'
    ],
    [
        // description مفقود عمداً
        'quantity' => '1',
        'unit_price' => '200'
    ]
];

try {
    $subtotal = 0;
    $items = [];
    
    if (isset($_POST['items']) && is_array($_POST['items'])) {
        foreach ($_POST['items'] as $item) {
            $description = $item['description'] ?? '';
            $quantity = $item['quantity'] ?? '';
            $unit_price = $item['unit_price'] ?? '';
            
            if (!empty($description) && !empty($quantity) && !empty($unit_price)) {
                $quantity = (float)$quantity;
                $unit_price = (float)$unit_price;
                $total_price = $quantity * $unit_price;
                $subtotal += $total_price;
                
                $items[] = [
                    'description' => sanitize($description),
                    'quantity' => $quantity,
                    'unit_price' => $unit_price,
                    'total_price' => $total_price
                ];
            }
        }
    }
    
    echo "<div class='result success'>✅ معالجة عناصر الفاتورة نجحت</div>";
    echo "<div class='result info'>";
    echo "عدد العناصر المعالجة: " . count($items) . "<br>";
    echo "المجموع الفرعي: " . formatCurrency($subtotal) . "<br>";
    echo "</div>";
    
    if (!empty($items)) {
        echo "<div class='result info'>";
        echo "<strong>العناصر:</strong><br>";
        foreach ($items as $item) {
            echo "- " . $item['description'] . " (الكمية: " . $item['quantity'] . ", السعر: " . formatCurrency($item['unit_price']) . ")<br>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في معالجة العناصر: " . $e->getMessage() . "</div>";
}

// اختبار htmlspecialchars مع قيم null
echo "<h2>5. اختبار htmlspecialchars مع قيم null</h2>";

$testData = [
    'name' => 'أحمد محمد',
    'email' => null,
    'phone' => '',
    'address' => 'شارع الملك فهد'
];

try {
    echo "<div class='result success'>";
    echo "الاسم: " . htmlspecialchars($testData['name'] ?? '') . "<br>";
    echo "البريد: " . htmlspecialchars($testData['email'] ?? '') . "<br>";
    echo "الهاتف: " . htmlspecialchars($testData['phone'] ?? '') . "<br>";
    echo "العنوان: " . htmlspecialchars($testData['address'] ?? '') . "<br>";
    echo "</div>";
    echo "<div class='result success'>✅ htmlspecialchars يعمل بدون تحذيرات</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في htmlspecialchars: " . $e->getMessage() . "</div>";
}

echo "<h2>6. النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 تم إصلاح جميع التحذيرات!</h3>";
echo "<p><strong>ما تم إصلاحه:</strong></p>";
echo "<ul>";
echo "<li>✅ دالة sanitize() تتعامل مع القيم الفارغة والـ null</li>";
echo "<li>✅ معالجة بيانات POST مع القيم المفقودة</li>";
echo "<li>✅ معالجة عناصر الفاتورة بأمان</li>";
echo "<li>✅ htmlspecialchars مع حماية من القيم الفارغة</li>";
echo "<li>✅ دوال التنسيق تعمل بدون تحذيرات</li>";
echo "</ul>";
echo "</div>";

echo "<h3>اختبار الصفحات المُصححة:</h3>";
echo "<a href='create-invoice.php' class='btn' target='_blank'>🔗 إنشاء فاتورة</a>";
echo "<a href='clients.php' class='btn' target='_blank'>🔗 إدارة العملاء</a>";
echo "<a href='settings.php' class='btn' target='_blank'>🔗 الإعدادات</a>";
echo "<a href='dashboard.php' class='btn' target='_blank'>🔗 لوحة التحكم</a>";

// تنظيف البيانات التجريبية
unset($_POST);

echo "</body></html>";
?>
