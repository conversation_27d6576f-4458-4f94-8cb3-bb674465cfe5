<?php
require_once '../config/config.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    redirect(APP_URL . '/dashboard.php');
}

$errors = [];
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $first_name = sanitize($_POST['first_name']);
    $last_name = sanitize($_POST['last_name']);
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $company_name = sanitize($_POST['company_name']);
    $phone = sanitize($_POST['phone']);
    $terms = isset($_POST['terms']);
    
    // التحقق من البيانات
    if (empty($first_name)) {
        $errors[] = 'يرجى إدخال الاسم الأول';
    }
    
    if (empty($last_name)) {
        $errors[] = 'يرجى إدخال الاسم الأخير';
    }
    
    if (empty($email)) {
        $errors[] = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($password)) {
        $errors[] = 'يرجى إدخال كلمة المرور';
    } elseif (strlen($password) < 6) {
        $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتان';
    }
    
    if (!$terms) {
        $errors[] = 'يجب الموافقة على شروط الاستخدام';
    }
    
    // التحقق من وجود البريد الإلكتروني
    if (empty($errors)) {
        try {
            $pdo = getDBConnection();
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                $errors[] = 'البريد الإلكتروني مستخدم بالفعل';
            }
        } catch (Exception $e) {
            $errors[] = 'حدث خطأ في النظام';
        }
    }
    
    // إنشاء الحساب
    if (empty($errors)) {
        try {
            $hashed_password = password_hash($password, HASH_ALGO);
            
            $stmt = $pdo->prepare("INSERT INTO users (first_name, last_name, email, password, company_name, phone, subscription_plan) VALUES (?, ?, ?, ?, ?, ?, 'free')");
            
            if ($stmt->execute([$first_name, $last_name, $email, $hashed_password, $company_name, $phone])) {
                $user_id = $pdo->lastInsertId();
                
                // تسجيل النشاط
                $logStmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
                $logStmt->execute([
                    $user_id,
                    'register',
                    'تسجيل مستخدم جديد',
                    $_SERVER['REMOTE_ADDR'],
                    $_SERVER['HTTP_USER_AGENT']
                ]);
                
                $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                
                // إعادة تعيين النموذج
                $_POST = [];
            } else {
                $errors[] = 'حدث خطأ في إنشاء الحساب';
            }
        } catch (Exception $e) {
            $errors[] = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}

$page_title = 'إنشاء حساب جديد';
include '../includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg">
                <div class="card-header text-center">
                    <h3><i class="fas fa-user-plus me-2"></i>إنشاء حساب جديد</h3>
                </div>
                <div class="card-body p-4">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                            <div class="mt-2">
                                <a href="login.php" class="btn btn-success btn-sm">تسجيل الدخول الآن</a>
                            </div>
                        </div>
                    <?php else: ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">الاسم الأول *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">
                                    يرجى إدخال الاسم الأول
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">
                                    يرجى إدخال الاسم الأخير
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                       required>
                                <div class="invalid-feedback">
                                    يرجى إدخال بريد إلكتروني صحيح
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           minlength="6" required>
                                    <div class="invalid-feedback">
                                        كلمة المرور يجب أن تكون 6 أحرف على الأقل
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           minlength="6" required>
                                    <div class="invalid-feedback">
                                        يرجى تأكيد كلمة المرور
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="company_name" class="form-label">اسم الشركة (اختياري)</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-building"></i></span>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="<?php echo isset($_POST['company_name']) ? htmlspecialchars($_POST['company_name']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف (اختياري)</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="../terms.php" target="_blank">شروط الاستخدام</a> و <a href="../privacy.php" target="_blank">سياسة الخصوصية</a> *
                            </label>
                            <div class="invalid-feedback">
                                يجب الموافقة على شروط الاستخدام
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>إنشاء الحساب
                            </button>
                        </div>
                    </form>
                    
                    <?php endif; ?>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">لديك حساب بالفعل؟</p>
                        <a href="login.php" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php include '../includes/footer.php'; ?>
