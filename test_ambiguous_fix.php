<?php
// اختبار إصلاح مشكلة التضارب في أسماء الأعمدة
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار إصلاح مشكلة التضارب</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 اختبار إصلاح مشكلة التضارب في أسماء الأعمدة</h1>";

// بدء الجلسة وتحميل التكوين
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// محاكاة تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
        $user = $stmt->fetch();
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            echo "<div class='result success'>✅ تم تسجيل الدخول للمستخدم ID: " . $user['id'] . "</div>";
        } else {
            echo "<div class='result error'>❌ لا يوجد مستخدمين</div>";
            exit;
        }
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في تسجيل الدخول: " . $e->getMessage() . "</div>";
        exit;
    }
}

$user_id = $_SESSION['user_id'];

// اختبار الاستعلام المُصحح
echo "<h2>1. اختبار الاستعلام المُصحح</h2>";
try {
    $pdo = getDBConnection();
    
    // محاكاة نفس المعاملات من clients.php
    $search = '';
    $per_page = 20;
    $offset = 0;
    
    // بناء الاستعلام مع تحديد الجداول بوضوح
    $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
    $params = [$user_id];
    
    if (!empty($search)) {
        $where_conditions[] = "(c.name LIKE ? OR c.email LIKE ? OR c.company LIKE ? OR c.phone LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    echo "<div class='result info'>";
    echo "<strong>WHERE clause:</strong> $where_clause<br>";
    echo "<strong>Parameters:</strong> " . implode(', ', $params);
    echo "</div>";
    
    // اختبار استعلام العد
    $countSql = "SELECT COUNT(*) FROM clients c WHERE $where_clause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total_clients = $countStmt->fetchColumn();
    
    echo "<div class='result success'>✅ استعلام العد نجح: $total_clients عميل</div>";
    
    // اختبار الاستعلام الرئيسي المُصحح
    $sql = "
        SELECT c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, 
               c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at,
               COALESCE(COUNT(i.id), 0) as invoice_count,
               COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END), 0) as total_paid,
               COALESCE(SUM(CASE WHEN i.status != 'paid' AND i.status IS NOT NULL THEN i.total_amount ELSE 0 END), 0) as total_pending,
               MAX(i.created_at) as last_invoice_date
        FROM clients c 
        LEFT JOIN invoices i ON c.id = i.client_id
        WHERE $where_clause 
        GROUP BY c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at
        ORDER BY c.name ASC 
        LIMIT $per_page OFFSET $offset
    ";
    
    echo "<div class='result info'>";
    echo "<strong>الاستعلام الرئيسي:</strong><br>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    echo "</div>";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $clients = $stmt->fetchAll();
    
    echo "<div class='result success'>✅ الاستعلام الرئيسي نجح!</div>";
    echo "<div class='result info'>عدد العملاء المسترجعة: " . count($clients) . "</div>";
    
    if (!empty($clients)) {
        echo "<div class='result success'>✅ تم العثور على بيانات العملاء!</div>";
        echo "<div class='result info'>";
        echo "<strong>عينة من البيانات:</strong><br>";
        foreach (array_slice($clients, 0, 3) as $client) {
            echo "- " . htmlspecialchars($client['name']) . " (" . htmlspecialchars($client['email'] ?? 'بدون بريد') . ")<br>";
        }
        echo "</div>";
    } else {
        echo "<div class='result warning'>⚠️ لم يتم العثور على عملاء</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاستعلام: " . $e->getMessage() . "</div>";
    
    // إذا كان الخطأ لا يزال متعلقاً بالتضارب، اعرض تفاصيل أكثر
    if (strpos($e->getMessage(), 'ambiguous') !== false) {
        echo "<div class='result error'>❌ لا يزال هناك تضارب في أسماء الأعمدة!</div>";
        echo "<div class='result info'>تفاصيل الخطأ: " . $e->getMessage() . "</div>";
    }
}

// اختبار الاستعلام البديل البسيط
echo "<h2>2. اختبار الاستعلام البديل البسيط</h2>";
try {
    $pdo = getDBConnection();
    
    $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
    $params = [$user_id];
    $where_clause = implode(' AND ', $where_conditions);
    
    $simpleSql = "SELECT c.*, 0 as invoice_count, 0 as total_paid, 0 as total_pending, NULL as last_invoice_date 
                  FROM clients c
                  WHERE $where_clause 
                  ORDER BY c.name ASC 
                  LIMIT 20 OFFSET 0";
    
    $simpleStmt = $pdo->prepare($simpleSql);
    $simpleStmt->execute($params);
    $simpleClients = $simpleStmt->fetchAll();
    
    echo "<div class='result success'>✅ الاستعلام البديل البسيط نجح!</div>";
    echo "<div class='result info'>عدد العملاء: " . count($simpleClients) . "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاستعلام البديل: " . $e->getMessage() . "</div>";
}

// إضافة عميل تجريبي إذا لم يوجد
echo "<h2>3. إضافة بيانات تجريبية</h2>";
if ($_POST['action'] ?? '' == 'add_sample') {
    try {
        $pdo = getDBConnection();
        
        $name = 'عميل تجريبي ' . date('H:i:s');
        $email = 'test' . time() . '@example.com';
        
        $stmt = $pdo->prepare("
            INSERT INTO clients (user_id, name, email, phone, company, is_active)
            VALUES (?, ?, ?, ?, ?, 1)
        ");
        
        $stmt->execute([$user_id, $name, $email, '0501234567', 'شركة تجريبية']);
        
        echo "<div class='result success'>✅ تم إضافة عميل تجريبي: $name</div>";
        
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في إضافة العميل: " . $e->getMessage() . "</div>";
    }
}

echo "<form method='POST'>";
echo "<input type='hidden' name='action' value='add_sample'>";
echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>إضافة عميل تجريبي</button>";
echo "</form>";

echo "<h2>4. النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 تم إصلاح مشكلة التضارب!</h3>";
echo "<p><strong>ما تم إصلاحه:</strong></p>";
echo "<ul>";
echo "<li>✅ إضافة البادئة 'c.' لجميع أعمدة جدول العملاء</li>";
echo "<li>✅ إصلاح WHERE clause لتجنب التضارب</li>";
echo "<li>✅ إصلاح ORDER BY clause</li>";
echo "<li>✅ إصلاح الاستعلام البديل</li>";
echo "</ul>";
echo "</div>";

echo "<h3>اختبار الصفحة المُصححة:</h3>";
echo "<a href='clients.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔗 صفحة العملاء</a>";
echo "<a href='clients_simple.php' target='_blank' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔗 النسخة البسيطة</a>";

echo "</body></html>";
?>
