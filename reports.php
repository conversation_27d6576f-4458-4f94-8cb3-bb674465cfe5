<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

// معاملات التقرير
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01'); // بداية الشهر الحالي
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-t'); // نهاية الشهر الحالي
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'monthly';

try {
    $pdo = getDBConnection();
    
    // إحصائيات عامة
    $generalStats = $pdo->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
            COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_invoices,
            COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_invoices,
            COUNT(CASE WHEN status = 'sent' AND due_date < CURDATE() THEN 1 END) as overdue_invoices,
            SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_revenue,
            SUM(CASE WHEN status != 'paid' AND status != 'cancelled' THEN total_amount ELSE 0 END) as pending_amount,
            AVG(CASE WHEN status = 'paid' THEN total_amount END) as avg_invoice_amount
        FROM invoices 
        WHERE user_id = ? AND issue_date BETWEEN ? AND ?
    ");
    $generalStats->execute([$user_id, $date_from, $date_to]);
    $stats = $generalStats->fetch();
    
    // إحصائيات شهرية للرسم البياني
    $monthlyStats = $pdo->prepare("
        SELECT 
            DATE_FORMAT(issue_date, '%Y-%m') as month,
            COUNT(*) as invoice_count,
            SUM(total_amount) as total_amount,
            SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as paid_amount
        FROM invoices 
        WHERE user_id = ? AND issue_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(issue_date, '%Y-%m')
        ORDER BY month ASC
    ");
    $monthlyStats->execute([$user_id]);
    $monthly = $monthlyStats->fetchAll();
    
    // أفضل العملاء
    $topClients = $pdo->prepare("
        SELECT 
            c.name,
            COUNT(i.id) as invoice_count,
            SUM(i.total_amount) as total_amount,
            SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as paid_amount
        FROM clients c
        LEFT JOIN invoices i ON c.id = i.client_id AND i.issue_date BETWEEN ? AND ?
        WHERE c.user_id = ? AND c.is_active = 1
        GROUP BY c.id, c.name
        HAVING invoice_count > 0
        ORDER BY total_amount DESC
        LIMIT 10
    ");
    $topClients->execute([$date_from, $date_to, $user_id]);
    $clients = $topClients->fetchAll();
    
    // إحصائيات حالة الفواتير
    $statusStats = $pdo->prepare("
        SELECT 
            status,
            COUNT(*) as count,
            SUM(total_amount) as amount
        FROM invoices 
        WHERE user_id = ? AND issue_date BETWEEN ? AND ?
        GROUP BY status
        ORDER BY amount DESC
    ");
    $statusStats->execute([$user_id, $date_from, $date_to]);
    $statuses = $statusStats->fetchAll();
    
    // الفواتير المتأخرة
    $overdueInvoices = $pdo->prepare("
        SELECT i.*, c.name as client_name,
               DATEDIFF(CURDATE(), i.due_date) as days_overdue
        FROM invoices i 
        LEFT JOIN clients c ON i.client_id = c.id 
        WHERE i.user_id = ? AND i.status = 'sent' AND i.due_date < CURDATE()
        ORDER BY i.due_date ASC
        LIMIT 10
    ");
    $overdueInvoices->execute([$user_id]);
    $overdue = $overdueInvoices->fetchAll();
    
} catch (Exception $e) {
    $stats = ['total_invoices' => 0, 'paid_invoices' => 0, 'sent_invoices' => 0, 'draft_invoices' => 0, 'overdue_invoices' => 0, 'total_revenue' => 0, 'pending_amount' => 0, 'avg_invoice_amount' => 0];
    $monthly = [];
    $clients = [];
    $statuses = [];
    $overdue = [];
}

$page_title = 'التقارير والإحصائيات';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h2><i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات</h2>
            <p class="text-muted">تحليل مفصل لأداء أعمالك</p>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <button class="btn btn-outline-primary" onclick="exportReport()">
                    <i class="fas fa-download me-1"></i>تصدير التقرير
                </button>
                <button class="btn btn-outline-secondary" onclick="printReport()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
            </div>
        </div>
    </div>
    
    <!-- Date Range Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" name="date_from" 
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" name="date_to" 
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">نوع التقرير</label>
                    <select class="form-select" name="report_type">
                        <option value="monthly" <?php echo $report_type === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                        <option value="quarterly" <?php echo $report_type === 'quarterly' ? 'selected' : ''; ?>>ربع سنوي</option>
                        <option value="yearly" <?php echo $report_type === 'yearly' ? 'selected' : ''; ?>>سنوي</option>
                        <option value="custom" <?php echo $report_type === 'custom' ? 'selected' : ''; ?>>مخصص</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i>تحديث التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Summary Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card bg-primary">
                <div class="stats-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number"><?php echo number_format($stats['total_invoices']); ?></div>
                    <div class="stats-label">إجمالي الفواتير</div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card bg-success">
                <div class="stats-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number"><?php echo formatCurrency($stats['total_revenue']); ?></div>
                    <div class="stats-label">إجمالي الإيرادات</div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card bg-warning">
                <div class="stats-icon">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number"><?php echo formatCurrency($stats['pending_amount']); ?></div>
                    <div class="stats-label">المبالغ المعلقة</div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card bg-info">
                <div class="stats-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number"><?php echo formatCurrency($stats['avg_invoice_amount'] ?? 0); ?></div>
                    <div class="stats-label">متوسط قيمة الفاتورة</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <!-- Monthly Revenue Chart -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>الإيرادات الشهرية</h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Invoice Status Chart -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>حالة الفواتير</h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tables Row -->
    <div class="row g-4">
        <!-- Top Clients -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>أفضل العملاء</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($clients)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد بيانات عملاء في هذه الفترة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>الفواتير</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المدفوع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($clients as $client): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($client['name']); ?></td>
                                            <td><?php echo number_format($client['invoice_count']); ?></td>
                                            <td><?php echo formatCurrency($client['total_amount']); ?></td>
                                            <td><?php echo formatCurrency($client['paid_amount']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Overdue Invoices -->
        <div class="col-lg-6">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0 text-danger"><i class="fas fa-exclamation-triangle me-2"></i>الفواتير المتأخرة</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($overdue)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">لا توجد فواتير متأخرة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>أيام التأخير</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($overdue as $invoice): ?>
                                        <tr>
                                            <td>
                                                <a href="view-invoice.php?id=<?php echo $invoice['id']; ?>">
                                                    <?php echo htmlspecialchars($invoice['invoice_number']); ?>
                                                </a>
                                            </td>
                                            <td><?php echo htmlspecialchars($invoice['client_name']); ?></td>
                                            <td><?php echo formatCurrency($invoice['total_amount']); ?></td>
                                            <td>
                                                <span class="badge bg-danger">
                                                    <?php echo $invoice['days_overdue']; ?> يوم
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    color: white;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
    font-size: 0.85rem;
}

.table-sm td {
    padding: 0.5rem;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .stats-card {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }

    .stats-icon {
        margin-bottom: 10px;
    }
}
</style>

<script>
// بيانات الرسوم البيانية
const monthlyData = <?php echo json_encode($monthly); ?>;
const statusData = <?php echo json_encode($statuses); ?>;

// رسم بياني للإيرادات الشهرية
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' });
        }),
        datasets: [{
            label: 'إجمالي المبالغ',
            data: monthlyData.map(item => parseFloat(item.total_amount)),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'المبالغ المدفوعة',
            data: monthlyData.map(item => parseFloat(item.paid_amount)),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ريال';
                    }
                }
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        }
    }
});

// رسم بياني دائري لحالة الفواتير
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: statusData.map(item => {
            const statusNames = {
                'draft': 'مسودة',
                'sent': 'مرسلة',
                'paid': 'مدفوعة',
                'overdue': 'متأخرة',
                'cancelled': 'ملغاة'
            };
            return statusNames[item.status] || item.status;
        }),
        datasets: [{
            data: statusData.map(item => parseFloat(item.amount)),
            backgroundColor: [
                '#6c757d', // مسودة
                '#ffc107', // مرسلة
                '#28a745', // مدفوعة
                '#dc3545', // متأخرة
                '#343a40'  // ملغاة
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        return label + ': ' + value.toLocaleString() + ' ريال (' + percentage + '%)';
                    }
                }
            }
        }
    }
});

// وظائف التصدير والطباعة
function exportReport() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = 'export-report.php?' + params.toString();
}

function printReport() {
    window.print();
}

// تحديث التقرير عند تغيير نوع التقرير
document.querySelector('select[name="report_type"]').addEventListener('change', function() {
    const form = this.closest('form');
    const dateFrom = form.querySelector('input[name="date_from"]');
    const dateTo = form.querySelector('input[name="date_to"]');

    const today = new Date();

    switch(this.value) {
        case 'monthly':
            dateFrom.value = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
            dateTo.value = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0];
            break;
        case 'quarterly':
            const quarter = Math.floor(today.getMonth() / 3);
            dateFrom.value = new Date(today.getFullYear(), quarter * 3, 1).toISOString().split('T')[0];
            dateTo.value = new Date(today.getFullYear(), (quarter + 1) * 3, 0).toISOString().split('T')[0];
            break;
        case 'yearly':
            dateFrom.value = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
            dateTo.value = new Date(today.getFullYear(), 11, 31).toISOString().split('T')[0];
            break;
    }
});

// تحسين مظهر الطباعة
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style media="print">
@media print {
    .btn, .card-header .btn-group {
        display: none !important;
    }

    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .stats-card {
        background: #f8f9fa !important;
        color: #333 !important;
    }

    .row {
        margin: 0;
    }

    .col-lg-6, .col-lg-8, .col-lg-4 {
        width: 100%;
        margin-bottom: 20px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
