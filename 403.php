<?php
require_once 'config/config.php';

http_response_code(403);
$page_title = 'غير مسموح - 403';
include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6 text-center">
            <div class="error-page">
                <div class="error-number">403</div>
                <div class="error-icon">
                    <i class="fas fa-lock fa-5x text-danger mb-4"></i>
                </div>
                <h2 class="mb-3">الوصول مرفوض</h2>
                <p class="lead mb-4">عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة أو المورد المطلوب.</p>
                
                <div class="error-reasons">
                    <h6>الأسباب المحتملة:</h6>
                    <ul class="list-unstyled text-start">
                        <li><i class="fas fa-circle text-danger me-2" style="font-size: 0.5rem;"></i>لم تسجل دخولك بعد</li>
                        <li><i class="fas fa-circle text-danger me-2" style="font-size: 0.5rem;"></i>انتهت صلاحية جلستك</li>
                        <li><i class="fas fa-circle text-danger me-2" style="font-size: 0.5rem;"></i>ليس لديك الصلاحيات المطلوبة</li>
                        <li><i class="fas fa-circle text-danger me-2" style="font-size: 0.5rem;"></i>تم حظر حسابك مؤقتاً</li>
                    </ul>
                </div>
                
                <div class="error-actions">
                    <?php if (!isLoggedIn()): ?>
                        <a href="<?php echo APP_URL; ?>/auth/login.php" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                        <a href="<?php echo APP_URL; ?>/auth/register.php" class="btn btn-success btn-lg me-3">
                            <i class="fas fa-user-plus me-2"></i>إنشاء حساب
                        </a>
                    <?php else: ?>
                        <a href="<?php echo APP_URL; ?>/dashboard.php" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo APP_URL; ?>" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                    </a>
                </div>
                
                <div class="contact-support mt-5">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        إذا كنت تعتقد أن هذا خطأ، يرجى 
                        <a href="<?php echo APP_URL; ?>/contact.php" class="alert-link">التواصل مع الدعم الفني</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 40px 20px;
}

.error-number {
    font-size: 8rem;
    font-weight: bold;
    color: #dc3545;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.error-reasons {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin: 30px 0;
    text-align: center;
}

.error-reasons ul {
    max-width: 300px;
    margin: 15px auto 0;
}

.error-reasons li {
    padding: 5px 0;
    color: #6c757d;
}

.error-actions {
    margin: 30px 0;
}

.contact-support {
    max-width: 400px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .error-number {
        font-size: 6rem;
    }
    
    .error-actions {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .error-actions .btn {
        width: 100%;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
