<?php
// اختبار نظام الفواتير والعملاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار نظام الفواتير والعملاء</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo ".test-form { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🧪 اختبار نظام الفواتير والعملاء</h1>";

// بدء الجلسة وتحميل التكوين
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// محاكاة تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
        $user = $stmt->fetch();
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            echo "<div class='result success'>✅ تم تسجيل الدخول للمستخدم ID: " . $user['id'] . "</div>";
        } else {
            echo "<div class='result error'>❌ لا يوجد مستخدمين في قاعدة البيانات</div>";
            exit;
        }
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في تسجيل الدخول: " . $e->getMessage() . "</div>";
        exit;
    }
}

// اختبار قاعدة البيانات
echo "<h2>1. اختبار قاعدة البيانات</h2>";
try {
    $pdo = getDBConnection();
    
    // فحص الجداول المطلوبة
    $requiredTables = ['users', 'clients', 'invoices', 'invoice_items', 'invoice_templates'];
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='result success'>✅ جدول '$table' موجود</div>";
        } else {
            echo "<div class='result warning'>⚠️ جدول '$table' مفقود - سيتم إنشاؤه تلقائياً</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}

// اختبار إضافة عميل تجريبي
echo "<h2>2. اختبار إضافة عميل</h2>";
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_client'])) {
    try {
        $pdo = getDBConnection();
        $user_id = $_SESSION['user_id'];
        
        $name = 'عميل تجريبي ' . date('H:i:s');
        $email = 'test' . time() . '@example.com';
        $phone = '0501234567';
        $company = 'شركة تجريبية';
        $address = 'عنوان تجريبي';
        $city = 'الرياض';
        $country = 'السعودية';
        
        $stmt = $pdo->prepare("
            INSERT INTO clients (user_id, name, email, phone, company, address, city, country)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([$user_id, $name, $email, $phone, $company, $address, $city, $country]);
        $client_id = $pdo->lastInsertId();
        
        echo "<div class='result success'>✅ تم إضافة العميل بنجاح - ID: $client_id</div>";
        echo "<div class='result info'>الاسم: $name<br>البريد: $email<br>الشركة: $company</div>";
        
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في إضافة العميل: " . $e->getMessage() . "</div>";
    }
}

echo "<div class='test-form'>";
echo "<h4>اختبار إضافة عميل:</h4>";
echo "<form method='POST'>";
echo "<input type='hidden' name='test_client' value='1'>";
echo "<button type='submit' class='btn btn-primary'>إضافة عميل تجريبي</button>";
echo "</form>";
echo "</div>";

// اختبار إضافة فاتورة تجريبية
echo "<h2>3. اختبار إضافة فاتورة</h2>";
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_invoice'])) {
    try {
        $pdo = getDBConnection();
        $user_id = $_SESSION['user_id'];
        
        // البحث عن عميل موجود
        $stmt = $pdo->prepare("SELECT id FROM clients WHERE user_id = ? AND is_active = 1 LIMIT 1");
        $stmt->execute([$user_id]);
        $client = $stmt->fetch();
        
        if (!$client) {
            throw new Exception('لا يوجد عملاء - يرجى إضافة عميل أولاً');
        }
        
        $client_id = $client['id'];
        $invoice_number = 'INV-TEST-' . time();
        $title = 'فاتورة تجريبية';
        $issue_date = date('Y-m-d');
        $due_date = date('Y-m-d', strtotime('+30 days'));
        $subtotal = 1000.00;
        $tax_rate = 15.00;
        $tax_amount = ($subtotal * $tax_rate) / 100;
        $total_amount = $subtotal + $tax_amount;
        $notes = 'فاتورة تجريبية للاختبار';
        
        // إدراج الفاتورة
        $stmt = $pdo->prepare("
            INSERT INTO invoices (
                user_id, client_id, invoice_number, title, issue_date, due_date, 
                subtotal, tax_rate, tax_amount, total_amount, notes, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft')
        ");
        
        $stmt->execute([
            $user_id, $client_id, $invoice_number, $title, $issue_date, $due_date,
            $subtotal, $tax_rate, $tax_amount, $total_amount, $notes
        ]);
        
        $invoice_id = $pdo->lastInsertId();
        
        // إضافة عناصر الفاتورة
        $items = [
            ['خدمة تطوير موقع', 1, 800.00],
            ['خدمة استضافة', 1, 200.00]
        ];
        
        $itemStmt = $pdo->prepare("
            INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($items as $item) {
            $total_price = $item[1] * $item[2];
            $itemStmt->execute([$invoice_id, $item[0], $item[1], $item[2], $total_price]);
        }
        
        echo "<div class='result success'>✅ تم إنشاء الفاتورة بنجاح - ID: $invoice_id</div>";
        echo "<div class='result info'>";
        echo "رقم الفاتورة: $invoice_number<br>";
        echo "العنوان: $title<br>";
        echo "المبلغ الفرعي: " . formatCurrency($subtotal) . "<br>";
        echo "الضريبة ($tax_rate%): " . formatCurrency($tax_amount) . "<br>";
        echo "المبلغ الإجمالي: " . formatCurrency($total_amount) . "<br>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في إنشاء الفاتورة: " . $e->getMessage() . "</div>";
    }
}

echo "<div class='test-form'>";
echo "<h4>اختبار إنشاء فاتورة:</h4>";
echo "<form method='POST'>";
echo "<input type='hidden' name='test_invoice' value='1'>";
echo "<button type='submit' class='btn btn-primary'>إنشاء فاتورة تجريبية</button>";
echo "</form>";
echo "</div>";

// عرض البيانات الحالية
echo "<h2>4. البيانات الحالية</h2>";
try {
    $pdo = getDBConnection();
    $user_id = $_SESSION['user_id'];
    
    // عدد العملاء
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $clientCount = $stmt->fetchColumn();
    echo "<div class='result info'>👥 عدد العملاء: $clientCount</div>";
    
    // عدد الفواتير
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM invoices WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $invoiceCount = $stmt->fetchColumn();
    echo "<div class='result info'>📄 عدد الفواتير: $invoiceCount</div>";
    
    // إجمالي المبيعات
    $stmt = $pdo->prepare("SELECT SUM(total_amount) FROM invoices WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $totalSales = $stmt->fetchColumn() ?: 0;
    echo "<div class='result info'>💰 إجمالي المبيعات: " . formatCurrency($totalSales) . "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في جلب البيانات: " . $e->getMessage() . "</div>";
}

echo "<h2>5. النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 نظام الفواتير والعملاء جاهز!</h3>";
echo "<p>يمكنك الآن استخدام النظام لإضافة العملاء والفواتير</p>";
echo "</div>";

echo "<h3>روابط النظام:</h3>";
echo "<p><a href='clients.php' target='_blank'>👥 إدارة العملاء</a></p>";
echo "<p><a href='create-invoice.php' target='_blank'>📄 إنشاء فاتورة</a></p>";
echo "<p><a href='invoices.php' target='_blank'>📋 قائمة الفواتير</a></p>";
echo "<p><a href='dashboard.php' target='_blank'>🏠 لوحة التحكم</a></p>";

echo "</body></html>";
?>
