<?php
// إصلاح جذري لجدول العملاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح جذري لجدول العملاء</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo ".btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 إصلاح جذري لجدول العملاء</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// الاتصال بقاعدة البيانات
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// الخطوة 1: فحص الجداول الموجودة
echo "<h2>1. فحص الجداول الموجودة</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='result info'>";
    echo "<strong>الجداول الموجودة:</strong><br>";
    foreach ($tables as $table) {
        echo "- $table<br>";
    }
    echo "</div>";
    
    $clientsExists = in_array('clients', $tables);
    echo "<div class='result " . ($clientsExists ? 'success' : 'warning') . "'>";
    echo $clientsExists ? "✅ جدول clients موجود" : "⚠️ جدول clients غير موجود";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجداول: " . $e->getMessage() . "</div>";
}

// الخطوة 2: فحص هيكل جدول العملاء إذا كان موجوداً
if ($clientsExists) {
    echo "<h2>2. فحص هيكل جدول العملاء</h2>";
    try {
        $stmt = $pdo->query("DESCRIBE clients");
        $columns = $stmt->fetchAll();
        
        echo "<div class='result info'>";
        echo "<strong>أعمدة جدول العملاء:</strong><br>";
        $hasUserId = false;
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ") " . 
                 ($column['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . 
                 ($column['Key'] ? " [" . $column['Key'] . "]" : "") . "<br>";
            
            if ($column['Field'] == 'user_id') {
                $hasUserId = true;
            }
        }
        echo "</div>";
        
        if (!$hasUserId) {
            echo "<div class='result error'>❌ عمود user_id مفقود من جدول العملاء</div>";
            
            // حفظ البيانات الموجودة
            echo "<h3>حفظ البيانات الموجودة</h3>";
            $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
            $existingCount = $stmt->fetchColumn();
            echo "<div class='result info'>عدد السجلات الموجودة: $existingCount</div>";
            
            if ($existingCount > 0) {
                echo "<div class='result warning'>⚠️ يوجد بيانات في الجدول - سيتم حفظها</div>";
                
                // إنشاء نسخة احتياطية
                $pdo->exec("CREATE TABLE clients_backup AS SELECT * FROM clients");
                echo "<div class='result success'>✅ تم إنشاء نسخة احتياطية: clients_backup</div>";
            }
            
            // إضافة عمود user_id
            echo "<h3>إضافة عمود user_id</h3>";
            try {
                $pdo->exec("ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
                echo "<div class='result success'>✅ تم إضافة عمود user_id</div>";
                
                // إضافة فهرس
                $pdo->exec("ALTER TABLE clients ADD INDEX idx_user_id (user_id)");
                echo "<div class='result success'>✅ تم إضافة فهرس user_id</div>";
                
                // ربط البيانات الموجودة بأول مستخدم
                if ($existingCount > 0) {
                    $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
                    $firstUser = $stmt->fetch();
                    
                    if ($firstUser) {
                        $pdo->prepare("UPDATE clients SET user_id = ?")->execute([$firstUser['id']]);
                        echo "<div class='result success'>✅ تم ربط البيانات الموجودة بالمستخدم ID: " . $firstUser['id'] . "</div>";
                    }
                }
                
                $hasUserId = true;
                
            } catch (Exception $e) {
                echo "<div class='result error'>❌ خطأ في إضافة عمود user_id: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='result success'>✅ عمود user_id موجود</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في فحص هيكل الجدول: " . $e->getMessage() . "</div>";
        $clientsExists = false;
    }
}

// الخطوة 3: إنشاء جدول العملاء من الصفر إذا لم يكن موجوداً أو كان معطوباً
if (!$clientsExists || !$hasUserId) {
    echo "<h2>3. إنشاء جدول العملاء من الصفر</h2>";
    
    try {
        // حذف الجدول القديم إذا كان موجوداً ومعطوباً
        if ($clientsExists && !$hasUserId) {
            $pdo->exec("DROP TABLE IF EXISTS clients");
            echo "<div class='result warning'>⚠️ تم حذف الجدول القديم المعطوب</div>";
        }
        
        // إنشاء جدول جديد
        $createTableSql = "
        CREATE TABLE clients (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            company VARCHAR(100),
            address TEXT,
            city VARCHAR(50),
            country VARCHAR(50),
            tax_number VARCHAR(50),
            notes TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_email (email),
            INDEX idx_name (name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTableSql);
        echo "<div class='result success'>✅ تم إنشاء جدول العملاء الجديد</div>";
        
        // استعادة البيانات من النسخة الاحتياطية إذا كانت موجودة
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE 'clients_backup'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='result info'>استعادة البيانات من النسخة الاحتياطية...</div>";
                
                // الحصول على أول مستخدم
                $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
                $firstUser = $stmt->fetch();
                
                if ($firstUser) {
                    $userId = $firstUser['id'];
                    
                    // استعادة البيانات
                    $pdo->exec("
                        INSERT INTO clients (user_id, name, email, phone, company, address, city, country, tax_number, notes, is_active, created_at)
                        SELECT $userId, name, email, phone, company, address, city, country, tax_number, notes, is_active, created_at
                        FROM clients_backup
                    ");
                    
                    $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
                    $restoredCount = $stmt->fetchColumn();
                    echo "<div class='result success'>✅ تم استعادة $restoredCount سجل</div>";
                    
                    // حذف النسخة الاحتياطية
                    $pdo->exec("DROP TABLE clients_backup");
                    echo "<div class='result info'>تم حذف النسخة الاحتياطية</div>";
                }
            }
        } catch (Exception $e) {
            echo "<div class='result warning'>⚠️ لم يتم استعادة البيانات: " . $e->getMessage() . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في إنشاء الجدول: " . $e->getMessage() . "</div>";
        exit;
    }
}

// الخطوة 4: إضافة بيانات تجريبية
echo "<h2>4. إضافة بيانات تجريبية</h2>";
try {
    // الحصول على أول مستخدم
    $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
    $user = $stmt->fetch();
    
    if ($user) {
        $userId = $user['id'];
        
        // فحص إذا كان هناك عملاء
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
        $stmt->execute([$userId]);
        $clientCount = $stmt->fetchColumn();
        
        if ($clientCount < 3) {
            echo "<div class='result info'>إضافة عملاء تجريبيين...</div>";
            
            $sampleClients = [
                ['أحمد محمد علي', '<EMAIL>', '0501234567', 'شركة التقنية المتقدمة', 'شارع الملك فهد', 'الرياض', 'السعودية', '123456789', 'عميل مهم'],
                ['فاطمة سالم أحمد', '<EMAIL>', '0509876543', 'مؤسسة الإبداع التجاري', 'طريق الملك عبدالعزيز', 'جدة', 'السعودية', '987654321', 'عميل منتظم'],
                ['محمد عبدالله سعد', '<EMAIL>', '0551234567', 'شركة الحلول الذكية', 'شارع التحلية', 'الدمام', 'السعودية', '*********', 'عميل جديد'],
                ['نورا خالد محمد', '<EMAIL>', '0561234567', 'مجموعة الأعمال المتكاملة', 'حي الملقا', 'الرياض', 'السعودية', '*********', 'عميل كبير']
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO clients (user_id, name, email, phone, company, address, city, country, tax_number, notes, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
            ");
            
            $added = 0;
            foreach ($sampleClients as $client) {
                try {
                    $stmt->execute(array_merge([$userId], $client));
                    $added++;
                } catch (Exception $e) {
                    // تجاهل الأخطاء (قد يكون العميل موجود)
                }
            }
            
            echo "<div class='result success'>✅ تم إضافة $added عميل تجريبي</div>";
        } else {
            echo "<div class='result info'>يوجد $clientCount عميل بالفعل</div>";
        }
    } else {
        echo "<div class='result error'>❌ لا يوجد مستخدمين لربط العملاء بهم</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في إضافة البيانات التجريبية: " . $e->getMessage() . "</div>";
}

// الخطوة 5: اختبار الاستعلامات
echo "<h2>5. اختبار الاستعلامات</h2>";
try {
    // محاكاة تسجيل الدخول
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id'])) {
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
        $user = $stmt->fetch();
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
        }
    }
    
    if (isset($_SESSION['user_id'])) {
        $testUserId = $_SESSION['user_id'];
        
        // اختبار الاستعلام البسيط
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ? AND is_active = 1");
        $stmt->execute([$testUserId]);
        $count = $stmt->fetchColumn();
        echo "<div class='result success'>✅ الاستعلام البسيط نجح: $count عميل</div>";
        
        // اختبار الاستعلام المعقد (نفس الكود من clients.php)
        $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
        $params = [$testUserId];
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "
            SELECT c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, 
                   c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at,
                   COALESCE(COUNT(i.id), 0) as invoice_count,
                   COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END), 0) as total_paid,
                   COALESCE(SUM(CASE WHEN i.status != 'paid' AND i.status IS NOT NULL THEN i.total_amount ELSE 0 END), 0) as total_pending,
                   MAX(i.created_at) as last_invoice_date
            FROM clients c 
            LEFT JOIN invoices i ON c.id = i.client_id
            WHERE $where_clause 
            GROUP BY c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at
            ORDER BY c.name ASC 
            LIMIT 20 OFFSET 0
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $clients = $stmt->fetchAll();
        
        echo "<div class='result success'>✅ الاستعلام المعقد نجح: " . count($clients) . " عميل</div>";
        
        if (!empty($clients)) {
            echo "<div class='result info'>";
            echo "<strong>عينة من العملاء:</strong><br>";
            foreach (array_slice($clients, 0, 3) as $client) {
                echo "- " . htmlspecialchars($client['name']) . " (" . htmlspecialchars($client['email'] ?? 'بدون بريد') . ")<br>";
            }
            echo "</div>";
        }
        
    } else {
        echo "<div class='result warning'>⚠️ لا يمكن اختبار الاستعلام - لا يوجد مستخدم</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار الاستعلامات: " . $e->getMessage() . "</div>";
}

echo "<h2>6. النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 تم إصلاح جدول العملاء بشكل جذري!</h3>";
echo "<p><strong>ما تم عمله:</strong></p>";
echo "<ul>";
echo "<li>✅ فحص شامل لهيكل قاعدة البيانات</li>";
echo "<li>✅ إضافة عمود user_id مع الفهارس</li>";
echo "<li>✅ إنشاء جدول جديد إذا لزم الأمر</li>";
echo "<li>✅ حفظ واستعادة البيانات الموجودة</li>";
echo "<li>✅ إضافة بيانات تجريبية شاملة</li>";
echo "<li>✅ اختبار جميع الاستعلامات</li>";
echo "</ul>";
echo "</div>";

echo "<h3>اختبار الصفحة الآن:</h3>";
echo "<a href='clients.php' class='btn' target='_blank'>🔗 صفحة العملاء</a>";
echo "<a href='dashboard.php' class='btn' target='_blank'>🔗 لوحة التحكم</a>";

echo "</body></html>";
?>
