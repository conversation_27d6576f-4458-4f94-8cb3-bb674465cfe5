<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

$user = getCurrentUser();
$user_id = $user['id'];

// استلام البيانات
$name = sanitize($_POST['name'] ?? '');
$email = sanitize($_POST['email'] ?? '');
$phone = sanitize($_POST['phone'] ?? '');
$address = sanitize($_POST['address'] ?? '');

// التحقق من البيانات
if (empty($name)) {
    echo json_encode(['success' => false, 'message' => 'اسم العميل مطلوب']);
    exit;
}

if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'البريد الإلكتروني غير صحيح']);
    exit;
}

try {
    $pdo = getDBConnection();
    
    // التحقق من عدم وجود عميل بنفس الاسم
    $checkStmt = $pdo->prepare("SELECT id FROM clients WHERE user_id = ? AND name = ? AND is_active = 1");
    $checkStmt->execute([$user_id, $name]);
    
    if ($checkStmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'يوجد عميل بنفس الاسم بالفعل']);
        exit;
    }
    
    // إدراج العميل الجديد
    $stmt = $pdo->prepare("
        INSERT INTO clients (user_id, name, email, phone, address) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    if ($stmt->execute([$user_id, $name, $email, $phone, $address])) {
        $client_id = $pdo->lastInsertId();
        
        // تسجيل النشاط
        $logStmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, entity_type, entity_id, description) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $logStmt->execute([
            $user_id,
            'create',
            'client',
            $client_id,
            'إضافة عميل جديد: ' . $name
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ العميل بنجاح',
            'client_id' => $client_id,
            'client_data' => [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'address' => $address
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ في حفظ العميل']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
