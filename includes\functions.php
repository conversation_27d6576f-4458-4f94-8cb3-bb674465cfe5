<?php
/**
 * دوال مساعدة للتطبيق
 * Helper Functions
 */

// منع الوصول المباشر (تعطيل مؤقت للاختبار)
// if (!defined('CONFIG_LOADED')) {
//     die('Access denied');
// }

// Core functions are now defined in config.php to avoid duplicates

/**
 * دالة لعرض الرسائل
 */
function setMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

/**
 * دالة للحصول على الرسائل
 */
function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * دالة لتنسيق العملة
 */
function formatCurrency($amount, $currency = null) {
    if ($currency === null) {
        $currency = defined('DEFAULT_CURRENCY') ? DEFAULT_CURRENCY : 'USD';
    }
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * دالة لتنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * دالة لتوليد رقم فاتورة
 */
function generateInvoiceNumber($prefix = 'INV-') {
    return $prefix . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة لتشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة للتحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * دالة لتوليد رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * دالة للتحقق من CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * دالة لتوليد CSRF Token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken();
    }
    return $_SESSION['csrf_token'];
}

/**
 * دالة لتحديد نوع الملف
 */
function getMimeType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $mimeTypes = [
        'pdf' => 'application/pdf',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    return $mimeTypes[$extension] ?? 'application/octet-stream';
}

/**
 * دالة لتحويل الحجم إلى تنسيق قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * دالة لتسجيل الأخطاء
 */
function logError($message, $file = null, $line = null) {
    $logMessage = date('Y-m-d H:i:s') . " - ERROR: $message";
    if ($file) $logMessage .= " in $file";
    if ($line) $logMessage .= " on line $line";
    $logMessage .= PHP_EOL;
    
    $logFile = 'logs/error.log';
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    
    error_log($logMessage, 3, $logFile);
}

/**
 * دالة لتسجيل الأنشطة
 */
function logActivity($userId, $action, $description = '') {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $userId,
            $action,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        logError("Failed to log activity: " . $e->getMessage());
    }
}

/**
 * دالة للتحقق من الصلاحيات
 */
function hasPermission($permission) {
    $user = getCurrentUser();
    if (!$user) return false;
    
    // للتبسيط، سنعتبر جميع المستخدمين لديهم جميع الصلاحيات
    // يمكن تطوير نظام صلاحيات أكثر تعقيداً لاحقاً
    return true;
}

/**
 * دالة لإنشاء رابط آمن
 */
function createSecureUrl($page, $params = []) {
    $url = APP_URL . '/' . $page;
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    return $url;
}

/**
 * دالة للتحقق من قوة كلمة المرور
 */
function isStrongPassword($password) {
    // على الأقل 8 أحرف، حرف كبير، حرف صغير، رقم
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

/**
 * دالة لتنظيف اسم الملف
 */
function sanitizeFilename($filename) {
    // إزالة الأحرف الخطيرة
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    // منع الأسماء الخطيرة
    $dangerous = ['..', '.htaccess', '.htpasswd', 'index.php'];
    foreach ($dangerous as $danger) {
        if (stripos($filename, $danger) !== false) {
            $filename = str_replace($danger, '', $filename);
        }
    }
    return $filename;
}

/**
 * دالة للتحقق من نوع الملف المسموح
 */
function isAllowedFileType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $allowedTypes = defined('ALLOWED_FILE_TYPES') ? ALLOWED_FILE_TYPES : ['jpg', 'jpeg', 'png', 'pdf'];
    return in_array($extension, $allowedTypes);
}
?>
