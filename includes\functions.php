<?php
/**
 * Additional Helper Functions for Invoice SaaS
 * Core functions are defined in config/config.php
 */

// Prevent direct access
if (!defined('CONFIG_LOADED')) {
    die('Access denied');
}

/**
 * Generate invoice number
 */
function generateInvoiceNumber($prefix = 'INV-') {
    return $prefix . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Hash password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate random token
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken();
    }
    return $_SESSION['csrf_token'];
}

/**
 * Get file MIME type
 */
function getMimeType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $mimeTypes = [
        'pdf' => 'application/pdf',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    return $mimeTypes[$extension] ?? 'application/octet-stream';
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Log error messages
 */
function logError($message, $file = null, $line = null) {
    $logMessage = date('Y-m-d H:i:s') . " - ERROR: $message";
    if ($file) $logMessage .= " in $file";
    if ($line) $logMessage .= " on line $line";
    $logMessage .= PHP_EOL;

    $logFile = 'logs/error.log';
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }

    error_log($logMessage, 3, $logFile);
}

/**
 * Log user activities
 */
function logActivity($userId, $action, $description = '') {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $userId,
            $action,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        logError("Failed to log activity: " . $e->getMessage());
    }
}

/**
 * Check user permissions
 */
function hasPermission($permission) {
    $user = getCurrentUser();
    if (!$user) return false;

    // Simple permission system - all users have all permissions
    // Can be expanded for more complex permission system
    return true;
}

/**
 * Create secure URL
 */
function createSecureUrl($page, $params = []) {
    $url = APP_URL . '/' . $page;
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    return $url;
}

/**
 * Check password strength
 */
function isStrongPassword($password) {
    // At least 8 characters, uppercase, lowercase, number
    return strlen($password) >= 8 &&
           preg_match('/[A-Z]/', $password) &&
           preg_match('/[a-z]/', $password) &&
           preg_match('/[0-9]/', $password);
}

/**
 * Sanitize filename
 */
function sanitizeFilename($filename) {
    // Remove dangerous characters
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    // Prevent dangerous names
    $dangerous = ['..', '.htaccess', '.htpasswd', 'index.php'];
    foreach ($dangerous as $danger) {
        if (stripos($filename, $danger) !== false) {
            $filename = str_replace($danger, '', $filename);
        }
    }
    return $filename;
}

/**
 * Check allowed file type
 */
function isAllowedFileType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $allowedTypes = defined('ALLOWED_FILE_TYPES') ? ALLOWED_FILE_TYPES : ['jpg', 'jpeg', 'png', 'pdf'];
    return in_array($extension, $allowedTypes);
}
?>
