-- قاعدة بيانات موقع SaaS لتوليد الفواتير
-- Invoice SaaS Database Schema

CREATE DATABASE IF NOT EXISTS invoice_saas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE invoice_saas;

-- جد<PERSON><PERSON> المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON>HAR(50) NOT NULL,
    company_name VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
    subscription_expires_at DATETIME NULL,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    company VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    tax_number VARCHAR(50),
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول قوالب الفواتير
CREATE TABLE invoice_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category ENUM('classic', 'modern', 'creative', 'minimal', 'professional') DEFAULT 'classic',
    html_template LONGTEXT NOT NULL,
    css_styles LONGTEXT,
    is_premium BOOLEAN DEFAULT FALSE,
    preview_image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الفواتير
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    client_id INT NOT NULL,
    template_id INT NOT NULL,
    invoice_number VARCHAR(50) NOT NULL,
    title VARCHAR(200),
    issue_date DATE NOT NULL,
    due_date DATE,
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    currency VARCHAR(3) DEFAULT 'USD',
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_rate DECIMAL(5,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    notes TEXT,
    terms TEXT,
    payment_instructions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES invoice_templates(id),
    UNIQUE KEY unique_invoice_number (user_id, invoice_number)
);

-- جدول عناصر الفاتورة
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(8,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'credit_card', 'paypal', 'other') DEFAULT 'cash',
    reference_number VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- جدول الإعدادات
CREATE TABLE user_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_setting (user_id, setting_key)
);

-- جدول سجل الأنشطة
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- إدراج قوالب افتراضية
INSERT INTO invoice_templates (name, description, category, html_template, css_styles, preview_image) VALUES
('Classic Template', 'قالب كلاسيكي بسيط ومهني', 'classic', 
'<div class="invoice-classic">
    <div class="header">
        <h1>INVOICE</h1>
        <div class="invoice-info">
            <p><strong>Invoice #:</strong> {{invoice_number}}</p>
            <p><strong>Date:</strong> {{issue_date}}</p>
            <p><strong>Due Date:</strong> {{due_date}}</p>
        </div>
    </div>
    <div class="company-info">
        <h3>{{company_name}}</h3>
        <p>{{company_address}}</p>
    </div>
    <div class="client-info">
        <h4>Bill To:</h4>
        <p><strong>{{client_name}}</strong></p>
        <p>{{client_address}}</p>
    </div>
    <table class="items-table">
        <thead>
            <tr>
                <th>Description</th>
                <th>Qty</th>
                <th>Price</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            {{invoice_items}}
        </tbody>
    </table>
    <div class="totals">
        <p><strong>Subtotal: {{subtotal}}</strong></p>
        <p><strong>Tax: {{tax_amount}}</strong></p>
        <p><strong>Total: {{total_amount}}</strong></p>
    </div>
</div>',
'.invoice-classic { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
.header { display: flex; justify-content: space-between; margin-bottom: 30px; }
.items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
.items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
.totals { text-align: right; margin-top: 20px; }',
'classic-preview.jpg'),

('Modern Template', 'قالب حديث وأنيق', 'modern',
'<div class="invoice-modern">
    <div class="header-modern">
        <div class="logo-section">
            <h1>{{company_name}}</h1>
        </div>
        <div class="invoice-details">
            <h2>INVOICE</h2>
            <p>{{invoice_number}}</p>
            <p>{{issue_date}}</p>
        </div>
    </div>
    <div class="parties">
        <div class="from-section">
            <h4>From:</h4>
            <p>{{company_address}}</p>
        </div>
        <div class="to-section">
            <h4>To:</h4>
            <p><strong>{{client_name}}</strong></p>
            <p>{{client_address}}</p>
        </div>
    </div>
    <table class="modern-table">
        <thead>
            <tr>
                <th>Item</th>
                <th>Qty</th>
                <th>Rate</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            {{invoice_items}}
        </tbody>
    </table>
    <div class="summary">
        <div class="summary-row">
            <span>Subtotal:</span>
            <span>{{subtotal}}</span>
        </div>
        <div class="summary-row">
            <span>Tax:</span>
            <span>{{tax_amount}}</span>
        </div>
        <div class="summary-row total">
            <span>Total:</span>
            <span>{{total_amount}}</span>
        </div>
    </div>
</div>',
'.invoice-modern { font-family: "Segoe UI", sans-serif; max-width: 800px; margin: 0 auto; padding: 30px; color: #333; }
.header-modern { display: flex; justify-content: space-between; align-items: center; margin-bottom: 40px; border-bottom: 3px solid #007bff; padding-bottom: 20px; }
.modern-table { width: 100%; border-collapse: collapse; margin: 30px 0; }
.modern-table th { background: #f8f9fa; padding: 15px; border: none; }
.modern-table td { padding: 12px 15px; border-bottom: 1px solid #eee; }
.summary { margin-top: 30px; text-align: right; }
.summary-row { display: flex; justify-content: space-between; margin: 10px 0; }
.total { font-weight: bold; font-size: 1.2em; border-top: 2px solid #007bff; padding-top: 10px; }',
'modern-preview.jpg');
