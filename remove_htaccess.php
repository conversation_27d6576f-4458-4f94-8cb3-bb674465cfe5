<?php
// حذف ملف .htaccess مؤقتاً لحل المشكلة
echo "<h1>حذف ملف .htaccess</h1>";

if (file_exists('.htaccess')) {
    // نسخ احتياطي
    if (copy('.htaccess', '.htaccess.backup')) {
        echo "<p>✅ تم إنشاء نسخة احتياطية: .htaccess.backup</p>";
    }
    
    // حذف الملف
    if (unlink('.htaccess')) {
        echo "<p>✅ تم حذف ملف .htaccess</p>";
        echo "<p><strong>الآن جرب:</strong></p>";
        echo "<ul>";
        echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
        echo "<li><a href='contact.php'>صفحة التواصل</a></li>";
        echo "<li><a href='simple_test.php'>اختبار بسيط</a></li>";
        echo "</ul>";
    } else {
        echo "<p>❌ فشل في حذف ملف .htaccess</p>";
    }
} else {
    echo "<p>⚠️ ملف .htaccess غير موجود</p>";
}

echo "<p><a href='diagnose.php'>العودة للتشخيص</a></p>";
?>
