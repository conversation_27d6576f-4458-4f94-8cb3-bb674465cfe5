<?php
// استعادة ملف .htaccess
echo "<h1>استعادة ملف .htaccess</h1>";

if (file_exists('.htaccess.backup')) {
    if (copy('.htaccess.backup', '.htaccess')) {
        echo "<p>✅ تم استعادة ملف .htaccess من النسخة الاحتياطية</p>";
    } else {
        echo "<p>❌ فشل في استعادة ملف .htaccess</p>";
    }
} else {
    // إنشاء ملف .htaccess بسيط جداً
    $simple_htaccess = "# Simple htaccess
php_value upload_max_filesize 10M
php_value post_max_size 10M";
    
    if (file_put_contents('.htaccess', $simple_htaccess)) {
        echo "<p>✅ تم إنشاء ملف .htaccess بسيط</p>";
    } else {
        echo "<p>❌ فشل في إنشاء ملف .htaccess</p>";
    }
}

echo "<p><a href='diagnose.php'>العودة للتشخيص</a></p>";
?>
