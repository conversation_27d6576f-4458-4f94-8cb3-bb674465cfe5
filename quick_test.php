<?php
// اختبار سريع للنظام
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار سريع</h1>";

// اختبار PHP
echo "<h2>PHP</h2>";
echo "الإصدار: " . phpversion() . "<br>";
echo "الذاكرة: " . ini_get('memory_limit') . "<br>";

// اختبار الامتدادات
$extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json'];
echo "<h2>الامتدادات</h2>";
foreach ($extensions as $ext) {
    echo "$ext: " . (extension_loaded($ext) ? '✅' : '❌') . "<br>";
}

// اختبار الملفات
echo "<h2>الملفات</h2>";
$files = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($files as $file) {
    echo "$file: " . (file_exists($file) ? '✅' : '❌') . "<br>";
}

// اختبار قاعدة البيانات
echo "<h2>قاعدة البيانات</h2>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=invoice_saas;charset=utf8mb4', 'root', '');
    echo "الاتصال: ✅<br>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'invoice_saas'");
    $count = $stmt->fetchColumn();
    echo "عدد الجداول: $count<br>";
    
} catch (Exception $e) {
    echo "الاتصال: ❌ " . $e->getMessage() . "<br>";
}

echo "<h2>الروابط</h2>";
echo "<a href='fix_errors.php'>إصلاح الأخطاء</a><br>";
echo "<a href='contact_simple.php'>صفحة التواصل المبسطة</a><br>";
echo "<a href='index.php'>الصفحة الرئيسية</a><br>";
?>

<style>
body { font-family: Arial; margin: 20px; direction: rtl; }
h1, h2 { color: #333; }
a { color: #007bff; }
</style>
