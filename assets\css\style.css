/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --font-family: 'Cairo', sans-serif;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* التنسيق العام */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 20px 0;
}

/* الأزرار المخصصة */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: 10px 10px 0 0 !important;
}

/* الجداول */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* النماذج */
.form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

/* الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stats-card .stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* قوالب الفواتير */
.template-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.template-card:hover {
    border-color: var(--primary-color);
    transform: scale(1.02);
}

.template-card.selected {
    border-color: var(--success-color);
    background-color: #f8fff9;
}

.template-preview {
    height: 200px;
    background-size: cover;
    background-position: center;
    border-radius: 8px;
    margin-bottom: 15px;
}

/* الشريط الجانبي */
.sidebar {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    height: fit-content;
}

.sidebar .nav-link {
    color: #333;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    margin-left: 10px;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
}

/* الفواتير */
.invoice-preview {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.invoice-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.invoice-info {
    text-align: left;
}

.invoice-parties {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.party-info h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.invoice-items {
    margin-bottom: 30px;
}

.invoice-items table {
    width: 100%;
    border-collapse: collapse;
}

.invoice-items th,
.invoice-items td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.invoice-items th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.invoice-totals {
    text-align: left;
    margin-top: 20px;
}

.invoice-totals .total-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.invoice-totals .total-row.final {
    font-size: 1.2rem;
    font-weight: bold;
    border-top: 2px solid var(--primary-color);
    padding-top: 15px;
    margin-top: 15px;
}

/* الرسائل */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

/* التحميل */
.loading {
    display: none;
    text-align: center;
    padding: 20px;
}

.spinner-border {
    color: var(--primary-color);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .invoice-header {
        flex-direction: column;
        text-align: center;
    }
    
    .invoice-parties {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* تحسينات إضافية */
.text-light-50 {
    color: rgba(255,255,255,0.7) !important;
}

.text-light-50:hover {
    color: rgba(255,255,255,1) !important;
}

.social-links a {
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color) !important;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
