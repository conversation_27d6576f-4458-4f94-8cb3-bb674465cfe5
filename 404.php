<?php
require_once 'config/config.php';

http_response_code(404);
$page_title = 'الصفحة غير موجودة - 404';
include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6 text-center">
            <div class="error-page">
                <div class="error-number">404</div>
                <div class="error-icon">
                    <i class="fas fa-search fa-5x text-muted mb-4"></i>
                </div>
                <h2 class="mb-3">الصفحة غير موجودة</h2>
                <p class="lead mb-4">عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.</p>
                
                <div class="error-actions">
                    <a href="<?php echo APP_URL; ?>" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>العودة للخلف
                    </button>
                </div>
                
                <div class="search-section mt-5">
                    <h5>أو ابحث عما تريد:</h5>
                    <form class="d-flex justify-content-center mt-3">
                        <div class="input-group" style="max-width: 400px;">
                            <input type="text" class="form-control" placeholder="ابحث في الموقع...">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="helpful-links mt-5">
                    <h6>روابط مفيدة:</h6>
                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            <a href="<?php echo APP_URL; ?>/templates.php" class="link-card">
                                <i class="fas fa-th-large me-2"></i>معرض القوالب
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo APP_URL; ?>/pricing.php" class="link-card">
                                <i class="fas fa-tags me-2"></i>خطط الأسعار
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo APP_URL; ?>/help.php" class="link-card">
                                <i class="fas fa-question-circle me-2"></i>مركز المساعدة
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="<?php echo APP_URL; ?>/contact.php" class="link-card">
                                <i class="fas fa-envelope me-2"></i>تواصل معنا
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 40px 20px;
}

.error-number {
    font-size: 8rem;
    font-weight: bold;
    color: #007bff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.error-actions {
    margin: 30px 0;
}

.search-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
}

.helpful-links {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.link-card {
    display: block;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.link-card:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .error-number {
        font-size: 6rem;
    }
    
    .error-actions {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .error-actions .btn {
        width: 100%;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
