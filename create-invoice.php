<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

// جلب القالب المحدد
$selected_template = isset($_GET['template']) ? (int)$_GET['template'] : 1;

try {
    $pdo = getDBConnection();

    // التأكد من وجود جدول القوالب وإنشاؤه إذا لم يكن موجوداً
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS invoice_templates (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                html_content TEXT,
                css_content TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // إدراج قوالب افتراضية إذا لم تكن موجودة
        $checkTemplates = $pdo->query("SELECT COUNT(*) FROM invoice_templates");
        if ($checkTemplates->fetchColumn() == 0) {
            $pdo->exec("
                INSERT INTO invoice_templates (name, description, is_active) VALUES
                ('القالب الكلاسيكي', 'قالب بسيط وأنيق للفواتير', 1),
                ('القالب الحديث', 'قالب عصري بألوان جذابة', 1),
                ('القالب المهني', 'قالب احترافي للشركات', 1)
            ");
        }
    } catch (Exception $e) {
        // في حالة فشل إنشاء الجدول، نستخدم قوالب افتراضية
    }

    // جلب القوالب المتاحة
    $templatesStmt = $pdo->query("SELECT * FROM invoice_templates WHERE is_active = 1 ORDER BY name");
    $templates = $templatesStmt->fetchAll();
    
    // جلب العملاء
    $clientsStmt = $pdo->prepare("SELECT * FROM clients WHERE user_id = ? AND is_active = 1 ORDER BY name");
    $clientsStmt->execute([$user_id]);
    $clients = $clientsStmt->fetchAll();
    
    // جلب القالب المحدد
    $templateStmt = $pdo->prepare("SELECT * FROM invoice_templates WHERE id = ? AND is_active = 1");
    $templateStmt->execute([$selected_template]);
    $current_template = $templateStmt->fetch();
    
    if (!$current_template) {
        $current_template = $templates[0] ?? null;
        $selected_template = $current_template['id'] ?? 1;
    }
    
} catch (Exception $e) {
    $templates = [];
    $clients = [];
    $current_template = null;
}

// معالجة إرسال النموذج
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getDBConnection();

        // جلب بيانات النموذج
        $client_id = (int)($_POST['client_id'] ?? 0);
        $invoice_number = sanitize($_POST['invoice_number'] ?? '');
        $title = sanitize($_POST['title'] ?? '');
        $issue_date = sanitize($_POST['issue_date'] ?? '');
        $due_date = sanitize($_POST['due_date'] ?? '');
        $notes = sanitize($_POST['notes'] ?? '');
        $terms = sanitize($_POST['terms'] ?? '');
        $template_id = (int)($_POST['template_id'] ?? 1);

        // حساب المبالغ
        $subtotal = 0;
        $items = [];

        if (isset($_POST['items']) && is_array($_POST['items'])) {
            foreach ($_POST['items'] as $item) {
                $description = $item['description'] ?? '';
                $quantity = $item['quantity'] ?? '';
                $unit_price = $item['unit_price'] ?? '';

                if (!empty($description) && !empty($quantity) && !empty($unit_price)) {
                    $quantity = (float)$quantity;
                    $unit_price = (float)$unit_price;
                    $total_price = $quantity * $unit_price;
                    $subtotal += $total_price;

                    $items[] = [
                        'description' => sanitize($description),
                        'quantity' => $quantity,
                        'unit_price' => $unit_price,
                        'total_price' => $total_price
                    ];
                }
            }
        }

        $tax_rate = isset($_POST['tax_rate']) ? (float)$_POST['tax_rate'] : 0;
        $tax_amount = ($subtotal * $tax_rate) / 100;
        $discount_amount = isset($_POST['discount_amount']) ? (float)$_POST['discount_amount'] : 0;
        $total_amount = $subtotal + $tax_amount - $discount_amount;

        // التحقق من البيانات المطلوبة
        if (empty($client_id) || empty($invoice_number) || empty($issue_date)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }

        // التحقق من عدم تكرار رقم الفاتورة
        $checkStmt = $pdo->prepare("SELECT id FROM invoices WHERE invoice_number = ? AND user_id = ?");
        $checkStmt->execute([$invoice_number, $user_id]);
        if ($checkStmt->fetch()) {
            throw new Exception('رقم الفاتورة موجود بالفعل');
        }

        // إدراج الفاتورة
        $invoiceStmt = $pdo->prepare("
            INSERT INTO invoices (
                user_id, client_id, invoice_number, title, issue_date, due_date,
                subtotal, tax_rate, tax_amount, discount_amount, total_amount,
                notes, terms, template_id, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft')
        ");

        $invoiceStmt->execute([
            $user_id, $client_id, $invoice_number, $title, $issue_date, $due_date,
            $subtotal, $tax_rate, $tax_amount, $discount_amount, $total_amount,
            $notes, $terms, $template_id
        ]);

        $invoice_id = $pdo->lastInsertId();

        // إدراج عناصر الفاتورة
        if (!empty($items)) {
            $itemStmt = $pdo->prepare("
                INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ");

            foreach ($items as $item) {
                $itemStmt->execute([
                    $invoice_id,
                    $item['description'],
                    $item['quantity'],
                    $item['unit_price'],
                    $item['total_price']
                ]);
            }
        }

        $success = "تم حفظ الفاتورة بنجاح! رقم الفاتورة: " . $invoice_number;

        // إعادة تعيين النموذج
        $_POST = [];

    } catch (Exception $e) {
        $error = "خطأ في حفظ الفاتورة: " . $e->getMessage();
    }
}

$page_title = 'إنشاء فاتورة جديدة';
$additional_css = ['assets/css/invoice-creator.css'];
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Form Section -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>إنشاء فاتورة جديدة</h5>
                </div>
                <div class="card-body">
                    <form id="invoice-form" method="POST">
                        <!-- Template Selection -->
                        <div class="mb-4">
                            <label class="form-label">اختيار القالب</label>
                            <div class="template-selector">
                                <?php foreach ($templates as $template): ?>
                                    <div class="template-option <?php echo $template['id'] == $selected_template ? 'selected' : ''; ?>" 
                                         data-template-id="<?php echo $template['id']; ?>">
                                        <div class="template-preview-mini">
                                            <?php if ($template['preview_image']): ?>
                                                <img src="assets/images/templates/<?php echo $template['preview_image']; ?>" 
                                                     alt="<?php echo htmlspecialchars($template['name']); ?>">
                                            <?php else: ?>
                                                <div class="template-placeholder-mini">
                                                    <i class="fas fa-file-invoice"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="template-name"><?php echo htmlspecialchars($template['name']); ?></div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <input type="hidden" name="template_id" id="template_id" value="<?php echo $selected_template; ?>">
                        </div>
                        
                        <!-- Invoice Details -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="invoice_number" class="form-label">رقم الفاتورة</label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                       value="INV-<?php echo date('Y-m-d-') . rand(1000, 9999); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="issue_date" class="form-label">تاريخ الإصدار</label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" 
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" 
                                       value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="currency" class="form-label">العملة</label>
                                <select class="form-select" id="currency" name="currency">
                                    <option value="SAR">ريال سعودي (SAR)</option>
                                    <option value="USD">دولار أمريكي (USD)</option>
                                    <option value="EUR">يورو (EUR)</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Client Selection -->
                        <div class="mb-3">
                            <label for="client_id" class="form-label">العميل</label>
                            <div class="input-group">
                                <select class="form-select" id="client_id" name="client_id" required>
                                    <option value="">اختر العميل</option>
                                    <?php foreach ($clients as $client): ?>
                                        <option value="<?php echo $client['id']; ?>"
                                                data-name="<?php echo htmlspecialchars($client['name'] ?? ''); ?>"
                                                data-email="<?php echo htmlspecialchars($client['email'] ?? ''); ?>"
                                                data-phone="<?php echo htmlspecialchars($client['phone'] ?? ''); ?>"
                                                data-address="<?php echo htmlspecialchars($client['address'] ?? ''); ?>">
                                            <?php echo htmlspecialchars($client['name'] ?? ''); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Invoice Items -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <label class="form-label mb-0">عناصر الفاتورة</label>
                                <button type="button" class="btn btn-sm btn-primary add-invoice-item">
                                    <i class="fas fa-plus me-1"></i>إضافة عنصر
                                </button>
                            </div>
                            
                            <div id="invoice-items">
                                <div class="invoice-item mb-3">
                                    <div class="row g-2">
                                        <div class="col-md-5">
                                            <input type="text" class="form-control" name="items[0][description]" 
                                                   placeholder="وصف الخدمة أو المنتج" required>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="number" class="form-control item-quantity" name="items[0][quantity]" 
                                                   placeholder="الكمية" value="1" min="0" step="0.01" required>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="number" class="form-control item-price" name="items[0][unit_price]" 
                                                   placeholder="السعر" min="0" step="0.01" required>
                                        </div>
                                        <div class="col-md-2">
                                            <input type="text" class="form-control item-total" readonly placeholder="المجموع">
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-outline-danger btn-sm remove-invoice-item">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Totals -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                                    <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                           value="15" min="0" max="100" step="0.01">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="discount_rate" class="form-label">معدل الخصم (%)</label>
                                    <input type="number" class="form-control" id="discount_rate" name="discount_rate" 
                                           value="0" min="0" max="100" step="0.01">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="totals-summary">
                                    <div class="total-row">
                                        <span>المجموع الفرعي:</span>
                                        <span id="subtotal">0.00</span>
                                    </div>
                                    <div class="total-row">
                                        <span>الضريبة:</span>
                                        <span id="tax-amount">0.00</span>
                                    </div>
                                    <div class="total-row">
                                        <span>الخصم:</span>
                                        <span id="discount-amount">0.00</span>
                                    </div>
                                    <div class="total-row final-total">
                                        <span>المجموع النهائي:</span>
                                        <span id="total-amount">0.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notes -->
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                        
                        <!-- Hidden Fields -->
                        <input type="hidden" id="hidden-subtotal" name="subtotal">
                        <input type="hidden" id="hidden-tax-amount" name="tax_amount">
                        <input type="hidden" id="hidden-discount-amount" name="discount_amount">
                        <input type="hidden" id="hidden-total" name="total_amount">
                        
                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ كمسودة
                            </button>
                            <button type="button" class="btn btn-success" onclick="previewInvoice()">
                                <i class="fas fa-eye me-1"></i>معاينة
                            </button>
                            <button type="submit" name="action" value="send" class="btn btn-info">
                                <i class="fas fa-paper-plane me-1"></i>حفظ وإرسال
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Preview Section -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-eye me-2"></i>معاينة مباشرة</h5>
                </div>
                <div class="card-body">
                    <div id="live-preview" class="invoice-preview">
                        <div class="text-center py-5 text-muted">
                            <i class="fas fa-file-invoice fa-3x mb-3"></i>
                            <p>املأ البيانات لرؤية المعاينة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Client Modal -->
<div class="modal fade" id="addClientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-client-form">
                    <div class="mb-3">
                        <label for="client_name" class="form-label">اسم العميل *</label>
                        <input type="text" class="form-control" id="client_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="client_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="client_email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="client_phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="client_phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="client_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="client_address" name="address" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveClient()">حفظ العميل</button>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Item Template -->
<script type="text/template" id="invoice-item-template">
    <div class="invoice-item mb-3">
        <div class="row g-2">
            <div class="col-md-5">
                <input type="text" class="form-control" name="items[[INDEX]][description]" 
                       placeholder="وصف الخدمة أو المنتج" required>
            </div>
            <div class="col-md-2">
                <input type="number" class="form-control item-quantity" name="items[[INDEX]][quantity]" 
                       placeholder="الكمية" value="1" min="0" step="0.01" required>
            </div>
            <div class="col-md-2">
                <input type="number" class="form-control item-price" name="items[[INDEX]][unit_price]" 
                       placeholder="السعر" min="0" step="0.01" required>
            </div>
            <div class="col-md-2">
                <input type="text" class="form-control item-total" readonly placeholder="المجموع">
            </div>
            <div class="col-md-1">
                <button type="button" class="btn btn-outline-danger btn-sm remove-invoice-item">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    </div>
</script>

<?php
$additional_js = ['assets/js/invoice-creator.js'];
include 'includes/footer.php';
?>
