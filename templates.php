<?php
require_once 'config/config.php';

// جلب القوالب من قاعدة البيانات
try {
    $pdo = getDBConnection();
    
    // فلترة القوالب حسب الفئة
    $category_filter = isset($_GET['category']) ? $_GET['category'] : '';
    $search_query = isset($_GET['search']) ? sanitize($_GET['search']) : '';
    
    $sql = "SELECT * FROM invoice_templates WHERE is_active = 1";
    $params = [];
    
    if (!empty($category_filter)) {
        $sql .= " AND category = ?";
        $params[] = $category_filter;
    }
    
    if (!empty($search_query)) {
        $sql .= " AND (name LIKE ? OR description LIKE ?)";
        $params[] = "%$search_query%";
        $params[] = "%$search_query%";
    }
    
    $sql .= " ORDER BY name ASC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $templates = $stmt->fetchAll();
    
    // جلب الفئات المتاحة
    $categoriesStmt = $pdo->query("SELECT DISTINCT category FROM invoice_templates WHERE is_active = 1 ORDER BY category");
    $categories = $categoriesStmt->fetchAll();
    
} catch (Exception $e) {
    $templates = [];
    $categories = [];
}

$page_title = 'معرض القوالب';
include 'includes/header.php';
?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold mb-3">معرض القوالب</h1>
            <p class="lead">اختر من بين مجموعة واسعة من القوالب الاحترافية المصممة خصيصاً لمختلف أنواع الأعمال</p>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="GET" class="d-flex gap-3">
                <!-- البحث -->
                <div class="flex-grow-1">
                    <input type="text" class="form-control" name="search" placeholder="ابحث في القوالب..." 
                           value="<?php echo htmlspecialchars($search_query); ?>">
                </div>
                
                <!-- فلترة الفئات -->
                <div class="flex-shrink-0">
                    <select class="form-select" name="category" onchange="this.form.submit()">
                        <option value="">جميع الفئات</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['category']; ?>" 
                                    <?php echo $category_filter === $cat['category'] ? 'selected' : ''; ?>>
                                <?php 
                                $category_names = [
                                    'classic' => 'كلاسيكي',
                                    'modern' => 'حديث',
                                    'creative' => 'إبداعي',
                                    'minimal' => 'بسيط',
                                    'professional' => 'مهني'
                                ];
                                echo $category_names[$cat['category']] ?? $cat['category'];
                                ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        
        <div class="col-md-4 text-end">
            <span class="text-muted">
                <i class="fas fa-th-large me-1"></i>
                <?php echo count($templates); ?> قالب متاح
            </span>
        </div>
    </div>
    
    <!-- Templates Grid -->
    <?php if (empty($templates)): ?>
        <div class="row">
            <div class="col-12 text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>لا توجد قوالب متاحة</h4>
                <p class="text-muted">جرب تغيير معايير البحث أو الفلترة</p>
            </div>
        </div>
    <?php else: ?>
        <div class="row g-4">
            <?php foreach ($templates as $template): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="template-card h-100">
                        <div class="template-preview">
                            <?php if ($template['preview_image']): ?>
                                <img src="assets/images/templates/<?php echo $template['preview_image']; ?>" 
                                     alt="<?php echo htmlspecialchars($template['name']); ?>" 
                                     class="template-image">
                            <?php else: ?>
                                <div class="template-placeholder">
                                    <i class="fas fa-file-invoice fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Template Actions -->
                            <div class="template-actions">
                                <button class="btn btn-primary btn-sm" onclick="previewTemplate(<?php echo $template['id']; ?>)">
                                    <i class="fas fa-eye me-1"></i>معاينة
                                </button>
                                <?php if (isLoggedIn()): ?>
                                    <button class="btn btn-success btn-sm" onclick="useTemplate(<?php echo $template['id']; ?>)">
                                        <i class="fas fa-plus me-1"></i>استخدم
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="template-info">
                            <div class="template-header">
                                <h5 class="template-title"><?php echo htmlspecialchars($template['name']); ?></h5>
                                <div class="template-badges">
                                    <span class="badge bg-secondary">
                                        <?php 
                                        $category_names = [
                                            'classic' => 'كلاسيكي',
                                            'modern' => 'حديث',
                                            'creative' => 'إبداعي',
                                            'minimal' => 'بسيط',
                                            'professional' => 'مهني'
                                        ];
                                        echo $category_names[$template['category']] ?? $template['category'];
                                        ?>
                                    </span>
                                    <?php if ($template['is_premium']): ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-crown me-1"></i>مميز
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <p class="template-description">
                                <?php echo htmlspecialchars($template['description']); ?>
                            </p>
                            
                            <div class="template-footer">
                                <?php if (!isLoggedIn()): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        سجل دخولك لاستخدام هذا القالب
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Call to Action -->
    <?php if (!isLoggedIn()): ?>
        <div class="row mt-5">
            <div class="col-12">
                <div class="cta-card text-center p-5">
                    <h3 class="mb-3">جاهز لإنشاء فاتورتك الأولى؟</h3>
                    <p class="lead mb-4">انضم إلى آلاف المستخدمين واستمتع بإنشاء فواتير احترافية في دقائق</p>
                    <a href="auth/register.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-rocket me-2"></i>ابدأ مجاناً
                    </a>
                    <a href="auth/login.php" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة القالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="templatePreviewContent" class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <?php if (isLoggedIn()): ?>
                    <button type="button" class="btn btn-primary" id="useTemplateBtn">
                        <i class="fas fa-plus me-1"></i>استخدم هذا القالب
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.template-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.template-preview {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.template-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.template-card:hover .template-image {
    transform: scale(1.05);
}

.template-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
}

.template-actions {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.template-card:hover .template-actions {
    opacity: 1;
}

.template-info {
    padding: 20px;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.template-title {
    margin: 0;
    font-weight: 600;
}

.template-badges {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.template-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.cta-card {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 20px;
}
</style>

<script>
let currentTemplateId = null;

function previewTemplate(templateId) {
    currentTemplateId = templateId;
    
    // إظهار المودال
    const modal = new bootstrap.Modal(document.getElementById('templatePreviewModal'));
    modal.show();
    
    // تحميل معاينة القالب
    fetch('ajax/preview-template.php?id=' + templateId)
        .then(response => response.text())
        .then(html => {
            document.getElementById('templatePreviewContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('templatePreviewContent').innerHTML = 
                '<div class="alert alert-danger">حدث خطأ في تحميل المعاينة</div>';
        });
}

function useTemplate(templateId) {
    if (confirm('هل تريد إنشاء فاتورة جديدة باستخدام هذا القالب؟')) {
        window.location.href = 'create-invoice.php?template=' + templateId;
    }
}

// استخدام القالب من المودال
document.getElementById('useTemplateBtn')?.addEventListener('click', function() {
    if (currentTemplateId) {
        useTemplate(currentTemplateId);
    }
});
</script>

<?php include 'includes/footer.php'; ?>
