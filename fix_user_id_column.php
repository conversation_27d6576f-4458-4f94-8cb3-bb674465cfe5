<?php
// إصلاح مباشر لعمود user_id في جدول العملاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح عمود user_id</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo ".btn-success { background: #28a745; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 إصلاح عمود user_id في جدول العملاء</h1>";

// إضافة أزرار الأوامر المباشرة
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h3>⚡ أوامر مباشرة</h3>";
echo "<p>اختر الأمر المناسب لحل المشكلة:</p>";
echo "<a href='?action=add_column' class='btn' style='background: #28a745;'>🔧 إضافة عمود user_id فقط</a> ";
echo "<a href='?action=full_fix' class='btn' style='background: #007bff;'>🛠️ إصلاح شامل</a> ";
echo "<a href='?action=create_table' class='btn' style='background: #ffc107; color: #212529;'>📋 إنشاء جدول جديد</a> ";
echo "<a href='?action=diagnose' class='btn' style='background: #17a2b8;'>🔍 تشخيص المشكلة</a>";
echo "</div>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// الاتصال بقاعدة البيانات
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// معالجة الأوامر المباشرة
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    // أمر إضافة العمود فقط
    if ($action == 'add_column') {
    echo "<h2>🚀 تنفيذ أمر إضافة العمود مباشرة</h2>";

    try {
        // فحص وجود الجدول أولاً
        $stmt = $pdo->query("SHOW TABLES LIKE 'clients'");
        if ($stmt->rowCount() == 0) {
            throw new Exception('جدول العملاء غير موجود');
        }

        // فحص وجود العمود
        $stmt = $pdo->query("DESCRIBE clients");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (in_array('user_id', $columns)) {
            echo "<div class='result warning'>⚠️ عمود user_id موجود بالفعل</div>";
        } else {
            echo "<div class='result info'>🔄 جاري إضافة عمود user_id...</div>";

            // تنفيذ أمر إضافة العمود
            $pdo->exec("ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
            echo "<div class='result success'>✅ تم إضافة عمود user_id بنجاح</div>";

            // إضافة فهرس
            try {
                $pdo->exec("ALTER TABLE clients ADD INDEX idx_user_id (user_id)");
                echo "<div class='result success'>✅ تم إضافة فهرس user_id</div>";
            } catch (Exception $e) {
                echo "<div class='result warning'>⚠️ لم يتم إضافة الفهرس: " . $e->getMessage() . "</div>";
            }

            // ربط البيانات الموجودة بأول مستخدم
            $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
            $clientCount = $stmt->fetchColumn();

            if ($clientCount > 0) {
                $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
                $firstUser = $stmt->fetch();

                if ($firstUser) {
                    $pdo->prepare("UPDATE clients SET user_id = ? WHERE user_id = 1")->execute([$firstUser['id']]);
                    echo "<div class='result success'>✅ تم ربط $clientCount عميل بالمستخدم ID: " . $firstUser['id'] . "</div>";
                }
            }

            echo "<div class='result success'>";
            echo "<h3>🎉 تم إضافة العمود بنجاح!</h3>";
            echo "<p>يمكنك الآن اختبار صفحة العملاء</p>";
            echo "<a href='clients.php' class='btn' target='_blank'>🔗 اختبار صفحة العملاء</a>";
            echo "</div>";
        }

    } catch (Exception $e) {
        echo "<div class='result error'>❌ فشل في إضافة العمود: " . $e->getMessage() . "</div>";
        echo "<div class='result info'>سيتم المتابعة مع الإصلاح الشامل أدناه...</div>";
    }

    echo "<hr>";

    // أمر إنشاء جدول جديد
    } elseif ($action == 'create_table') {
        echo "<h2>📋 إنشاء جدول العملاء من الصفر</h2>";

        try {
            // فحص وجود الجدول
            $stmt = $pdo->query("SHOW TABLES LIKE 'clients'");
            $tableExists = $stmt->rowCount() > 0;

            if ($tableExists) {
                // حفظ البيانات الموجودة
                $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
                $existingCount = $stmt->fetchColumn();

                if ($existingCount > 0) {
                    echo "<div class='result warning'>⚠️ يوجد $existingCount سجل - سيتم حفظهم</div>";

                    try {
                        $pdo->exec("DROP TABLE IF EXISTS clients_backup");
                        $pdo->exec("CREATE TABLE clients_backup AS SELECT * FROM clients");
                        echo "<div class='result success'>✅ تم حفظ البيانات في clients_backup</div>";
                    } catch (Exception $e) {
                        echo "<div class='result error'>❌ فشل في حفظ البيانات: " . $e->getMessage() . "</div>";
                        throw new Exception('لا يمكن المتابعة بدون حفظ البيانات');
                    }
                }

                // حذف الجدول القديم
                $pdo->exec("DROP TABLE clients");
                echo "<div class='result warning'>⚠️ تم حذف الجدول القديم</div>";
            }

            // إنشاء جدول جديد
            $createSql = "
            CREATE TABLE clients (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL DEFAULT 1,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                company VARCHAR(100),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50),
                tax_number VARCHAR(50),
                notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_email (email)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $pdo->exec($createSql);
            echo "<div class='result success'>✅ تم إنشاء جدول العملاء الجديد</div>";

            // استعادة البيانات إذا كانت موجودة
            if ($tableExists && $existingCount > 0) {
                try {
                    $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
                    $firstUser = $stmt->fetch();
                    $userId = $firstUser ? $firstUser['id'] : 1;

                    $pdo->exec("
                        INSERT INTO clients (user_id, name, email, phone, company, address, city, country, tax_number, notes, is_active, created_at)
                        SELECT $userId, name, email, phone, company, address, city, country, tax_number, notes, is_active, created_at
                        FROM clients_backup
                    ");

                    echo "<div class='result success'>✅ تم استعادة $existingCount سجل</div>";

                    // حذف النسخة الاحتياطية
                    $pdo->exec("DROP TABLE clients_backup");
                    echo "<div class='result info'>تم حذف النسخة الاحتياطية</div>";

                } catch (Exception $e) {
                    echo "<div class='result error'>❌ فشل في استعادة البيانات: " . $e->getMessage() . "</div>";
                    echo "<div class='result warning'>⚠️ البيانات محفوظة في جدول clients_backup</div>";
                }
            }

            echo "<div class='result success'>";
            echo "<h3>🎉 تم إنشاء الجدول بنجاح!</h3>";
            echo "<p>الجدول يحتوي الآن على عمود user_id</p>";
            echo "<a href='clients.php' class='btn' target='_blank'>🔗 اختبار صفحة العملاء</a>";
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='result error'>❌ فشل في إنشاء الجدول: " . $e->getMessage() . "</div>";
        }

        echo "<hr>";

    // أمر تشخيص المشكلة
    } elseif ($action == 'diagnose') {
        echo "<h2>🔍 تشخيص مشكلة عدم ظهور البيانات</h2>";

        try {
            // فحص هيكل الجدول
            $stmt = $pdo->query("DESCRIBE clients");
            $columns = $stmt->fetchAll();

            echo "<div class='result info'>";
            echo "<strong>هيكل جدول العملاء:</strong><br>";
            foreach ($columns as $column) {
                echo "- " . $column['Field'] . " (" . $column['Type'] . ") " .
                     ($column['Null'] == 'YES' ? 'NULL' : 'NOT NULL') .
                     " Default: " . ($column['Default'] ?? 'NULL') . "<br>";
            }
            echo "</div>";

            // فحص إجمالي البيانات
            $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
            $totalClients = $stmt->fetchColumn();
            echo "<div class='result info'>إجمالي العملاء في الجدول: $totalClients</div>";

            if ($totalClients == 0) {
                echo "<div class='result warning'>⚠️ لا يوجد عملاء في الجدول - هذا سبب عدم ظهور البيانات</div>";
                echo "<div class='result info'>سيتم إضافة عملاء تجريبيين...</div>";

                // إضافة عملاء تجريبيين
                $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
                $user = $stmt->fetch();
                $userId = $user ? $user['id'] : 1;

                $sampleClients = [
                    ['أحمد محمد علي', '<EMAIL>', '0501234567', 'شركة التقنية المتقدمة'],
                    ['فاطمة سالم أحمد', '<EMAIL>', '0509876543', 'مؤسسة الإبداع التجاري'],
                    ['محمد عبدالله سعد', '<EMAIL>', '0551234567', 'شركة الحلول الذكية']
                ];

                $stmt = $pdo->prepare("
                    INSERT INTO clients (user_id, name, email, phone, company, is_active)
                    VALUES (?, ?, ?, ?, ?, 1)
                ");

                $added = 0;
                foreach ($sampleClients as $client) {
                    try {
                        $stmt->execute(array_merge([$userId], $client));
                        $added++;
                    } catch (Exception $e) {
                        // تجاهل الأخطاء
                    }
                }

                echo "<div class='result success'>✅ تم إضافة $added عميل تجريبي</div>";
            } else {
                // فحص توزيع العملاء على المستخدمين
                $stmt = $pdo->query("SELECT user_id, COUNT(*) as count FROM clients GROUP BY user_id");
                $distribution = $stmt->fetchAll();

                echo "<div class='result info'>";
                echo "<strong>توزيع العملاء على المستخدمين:</strong><br>";
                foreach ($distribution as $row) {
                    echo "- المستخدم ID " . $row['user_id'] . ": " . $row['count'] . " عميل<br>";
                }
                echo "</div>";

                // فحص العملاء النشطين
                $stmt = $pdo->query("SELECT COUNT(*) FROM clients WHERE is_active = 1");
                $activeClients = $stmt->fetchColumn();
                echo "<div class='result info'>العملاء النشطين: $activeClients</div>";

                if ($activeClients == 0) {
                    echo "<div class='result warning'>⚠️ لا يوجد عملاء نشطين - سيتم تفعيل جميع العملاء</div>";
                    $pdo->exec("UPDATE clients SET is_active = 1");
                    echo "<div class='result success'>✅ تم تفعيل جميع العملاء</div>";
                }
            }

            // فحص المستخدم الحالي
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            if (!isset($_SESSION['user_id'])) {
                $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
                $user = $stmt->fetch();
                if ($user) {
                    $_SESSION['user_id'] = $user['id'];
                    echo "<div class='result info'>تم تسجيل الدخول للمستخدم ID: " . $user['id'] . "</div>";
                }
            }

            if (isset($_SESSION['user_id'])) {
                $currentUserId = $_SESSION['user_id'];
                echo "<div class='result info'>المستخدم الحالي: ID $currentUserId</div>";

                // فحص عملاء المستخدم الحالي
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
                $stmt->execute([$currentUserId]);
                $userClients = $stmt->fetchColumn();

                echo "<div class='result info'>عملاء المستخدم الحالي: $userClients</div>";

                if ($userClients == 0) {
                    echo "<div class='result warning'>⚠️ المستخدم الحالي ليس له عملاء</div>";
                    echo "<div class='result info'>سيتم ربط بعض العملاء بالمستخدم الحالي...</div>";

                    // ربط أول 3 عملاء بالمستخدم الحالي
                    $stmt = $pdo->prepare("UPDATE clients SET user_id = ? WHERE id IN (SELECT id FROM (SELECT id FROM clients LIMIT 3) as temp)");
                    $stmt->execute([$currentUserId]);

                    echo "<div class='result success'>✅ تم ربط عملاء بالمستخدم الحالي</div>";
                }

                // اختبار الاستعلام من clients.php
                echo "<h3>اختبار الاستعلام من clients.php</h3>";

                $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
                $params = [$currentUserId];
                $where_clause = implode(' AND ', $where_conditions);

                // استعلام العد
                $countSql = "SELECT COUNT(*) FROM clients c WHERE $where_clause";
                $countStmt = $pdo->prepare($countSql);
                $countStmt->execute($params);
                $count = $countStmt->fetchColumn();

                echo "<div class='result info'>استعلام العد: $count عميل</div>";

                if ($count > 0) {
                    // الاستعلام الرئيسي
                    $sql = "
                        SELECT c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country,
                               c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at,
                               COALESCE(COUNT(i.id), 0) as invoice_count,
                               COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END), 0) as total_paid,
                               COALESCE(SUM(CASE WHEN i.status != 'paid' AND i.status IS NOT NULL THEN i.total_amount ELSE 0 END), 0) as total_pending,
                               MAX(i.created_at) as last_invoice_date
                        FROM clients c
                        LEFT JOIN invoices i ON c.id = i.client_id
                        WHERE $where_clause
                        GROUP BY c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at
                        ORDER BY c.name ASC
                        LIMIT 5
                    ";

                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);
                    $clients = $stmt->fetchAll();

                    echo "<div class='result success'>✅ الاستعلام الرئيسي نجح: " . count($clients) . " عميل</div>";

                    if (!empty($clients)) {
                        echo "<div class='result info'>";
                        echo "<strong>عينة من العملاء:</strong><br>";
                        foreach ($clients as $client) {
                            echo "- " . htmlspecialchars($client['name']) . " (ID: " . $client['id'] . ", user_id: " . $client['user_id'] . ")<br>";
                        }
                        echo "</div>";

                        echo "<div class='result success'>";
                        echo "<h3>🎉 المشكلة محلولة!</h3>";
                        echo "<p>البيانات موجودة ويمكن جلبها بنجاح</p>";
                        echo "<a href='clients.php' class='btn' target='_blank'>🔗 اختبار صفحة العملاء</a>";
                        echo "</div>";
                    }
                } else {
                    echo "<div class='result error'>❌ لا يوجد عملاء للمستخدم الحالي حتى بعد الإصلاح</div>";
                }
            }

        } catch (Exception $e) {
            echo "<div class='result error'>❌ خطأ في التشخيص: " . $e->getMessage() . "</div>";
        }

        echo "<hr>";
    }
}

echo "<h2>الخطوة 1: فحص جدول العملاء</h2>";

// فحص وجود الجدول
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'clients'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<div class='result error'>❌ جدول العملاء غير موجود</div>";
        echo "<div class='result info'>سيتم إنشاء الجدول من الصفر...</div>";
        
        // إنشاء الجدول
        $createSql = "
        CREATE TABLE clients (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL DEFAULT 1,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            company VARCHAR(100),
            address TEXT,
            city VARCHAR(50),
            country VARCHAR(50),
            tax_number VARCHAR(50),
            notes TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createSql);
        echo "<div class='result success'>✅ تم إنشاء جدول العملاء مع عمود user_id</div>";
        
    } else {
        echo "<div class='result success'>✅ جدول العملاء موجود</div>";
        
        // فحص وجود عمود user_id
        $stmt = $pdo->query("DESCRIBE clients");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<div class='result info'>";
        echo "<strong>الأعمدة الموجودة:</strong> " . implode(', ', $columns);
        echo "</div>";
        
        if (!in_array('user_id', $columns)) {
            echo "<div class='result error'>❌ عمود user_id مفقود</div>";
            echo "<div class='result info'>سيتم إضافة العمود...</div>";
            
            // حفظ البيانات الموجودة
            $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
            $existingCount = $stmt->fetchColumn();
            echo "<div class='result info'>عدد السجلات الموجودة: $existingCount</div>";
            
            // إضافة العمود
            try {
                $pdo->exec("ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
                echo "<div class='result success'>✅ تم إضافة عمود user_id</div>";
                
                // إضافة فهرس
                try {
                    $pdo->exec("ALTER TABLE clients ADD INDEX idx_user_id (user_id)");
                    echo "<div class='result success'>✅ تم إضافة فهرس user_id</div>";
                } catch (Exception $e) {
                    echo "<div class='result warning'>⚠️ لم يتم إضافة الفهرس: " . $e->getMessage() . "</div>";
                }
                
                // ربط البيانات الموجودة بأول مستخدم
                if ($existingCount > 0) {
                    $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
                    $firstUser = $stmt->fetch();
                    
                    if ($firstUser) {
                        $pdo->prepare("UPDATE clients SET user_id = ? WHERE user_id = 1")->execute([$firstUser['id']]);
                        echo "<div class='result success'>✅ تم ربط $existingCount سجل بالمستخدم ID: " . $firstUser['id'] . "</div>";
                    } else {
                        echo "<div class='result warning'>⚠️ لا يوجد مستخدمين نشطين</div>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<div class='result error'>❌ فشل في إضافة العمود: " . $e->getMessage() . "</div>";
                echo "<div class='result warning'>⚠️ سيتم إعادة إنشاء الجدول...</div>";
                
                // حفظ البيانات
                if ($existingCount > 0) {
                    try {
                        $pdo->exec("DROP TABLE IF EXISTS clients_backup");
                        $pdo->exec("CREATE TABLE clients_backup AS SELECT * FROM clients");
                        echo "<div class='result success'>✅ تم حفظ البيانات في clients_backup</div>";
                    } catch (Exception $e2) {
                        echo "<div class='result error'>❌ فشل في حفظ البيانات: " . $e2->getMessage() . "</div>";
                    }
                }
                
                // حذف الجدول القديم وإنشاء جديد
                try {
                    $pdo->exec("DROP TABLE clients");
                    echo "<div class='result warning'>⚠️ تم حذف الجدول القديم</div>";
                    
                    $pdo->exec($createSql);
                    echo "<div class='result success'>✅ تم إنشاء جدول جديد</div>";
                    
                    // استعادة البيانات
                    if ($existingCount > 0) {
                        try {
                            $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
                            $firstUser = $stmt->fetch();
                            $userId = $firstUser ? $firstUser['id'] : 1;
                            
                            $pdo->exec("
                                INSERT INTO clients (user_id, name, email, phone, company, address, city, country, tax_number, notes, is_active, created_at)
                                SELECT $userId, name, email, phone, company, address, city, country, tax_number, notes, is_active, created_at
                                FROM clients_backup
                            ");
                            
                            echo "<div class='result success'>✅ تم استعادة $existingCount سجل</div>";
                            
                            // حذف النسخة الاحتياطية
                            $pdo->exec("DROP TABLE clients_backup");
                            echo "<div class='result info'>تم حذف النسخة الاحتياطية</div>";
                            
                        } catch (Exception $e3) {
                            echo "<div class='result error'>❌ فشل في استعادة البيانات: " . $e3->getMessage() . "</div>";
                        }
                    }
                    
                } catch (Exception $e2) {
                    echo "<div class='result error'>❌ فشل في إعادة إنشاء الجدول: " . $e2->getMessage() . "</div>";
                    exit;
                }
            }
            
        } else {
            echo "<div class='result success'>✅ عمود user_id موجود بالفعل</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</div>";
    exit;
}

echo "<h2>الخطوة 2: إضافة بيانات تجريبية</h2>";

try {
    // فحص عدد العملاء
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
    $clientCount = $stmt->fetchColumn();
    
    if ($clientCount == 0) {
        echo "<div class='result info'>لا يوجد عملاء - سيتم إضافة عملاء تجريبيين</div>";
        
        // الحصول على أول مستخدم
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
        $user = $stmt->fetch();
        
        if ($user) {
            $userId = $user['id'];
            
            $sampleClients = [
                ['أحمد محمد علي', '<EMAIL>', '0501234567', 'شركة التقنية المتقدمة'],
                ['فاطمة سالم أحمد', '<EMAIL>', '0509876543', 'مؤسسة الإبداع التجاري'],
                ['محمد عبدالله سعد', '<EMAIL>', '0551234567', 'شركة الحلول الذكية'],
                ['نورا خالد محمد', '<EMAIL>', '0561234567', 'مجموعة الأعمال المتكاملة']
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO clients (user_id, name, email, phone, company, is_active)
                VALUES (?, ?, ?, ?, ?, 1)
            ");
            
            $added = 0;
            foreach ($sampleClients as $client) {
                try {
                    $stmt->execute(array_merge([$userId], $client));
                    $added++;
                } catch (Exception $e) {
                    // تجاهل الأخطاء
                }
            }
            
            echo "<div class='result success'>✅ تم إضافة $added عميل تجريبي</div>";
        } else {
            echo "<div class='result warning'>⚠️ لا يوجد مستخدمين لربط العملاء بهم</div>";
        }
    } else {
        echo "<div class='result info'>يوجد $clientCount عميل في النظام</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في إضافة البيانات: " . $e->getMessage() . "</div>";
}

echo "<h2>الخطوة 3: اختبار الاستعلامات</h2>";

try {
    // محاكاة تسجيل الدخول
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id'])) {
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
        $user = $stmt->fetch();
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
        }
    }
    
    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
        
        // اختبار الاستعلام الذي كان يفشل
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients c WHERE c.user_id = ? AND c.is_active = 1");
        $stmt->execute([$userId]);
        $count = $stmt->fetchColumn();
        
        echo "<div class='result success'>✅ الاستعلام المُشكِل نجح: $count عميل</div>";
        
        // اختبار الاستعلام المعقد
        $sql = "
            SELECT c.*, 
                   COALESCE(COUNT(i.id), 0) as invoice_count
            FROM clients c 
            LEFT JOIN invoices i ON c.id = i.client_id
            WHERE c.user_id = ? AND c.is_active = 1
            GROUP BY c.id
            ORDER BY c.name ASC 
            LIMIT 5
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId]);
        $clients = $stmt->fetchAll();
        
        echo "<div class='result success'>✅ الاستعلام المعقد نجح: " . count($clients) . " عميل</div>";
        
        if (!empty($clients)) {
            echo "<div class='result info'>";
            echo "<strong>عينة من العملاء:</strong><br>";
            foreach ($clients as $client) {
                echo "- " . htmlspecialchars($client['name']) . " (ID: " . $client['user_id'] . ")<br>";
            }
            echo "</div>";
        }
        
    } else {
        echo "<div class='result warning'>⚠️ لا يمكن اختبار الاستعلامات - لا يوجد مستخدم</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار الاستعلامات: " . $e->getMessage() . "</div>";
}

echo "<h2>النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 تم إصلاح مشكلة عمود user_id!</h3>";
echo "<p><strong>ما تم عمله:</strong></p>";
echo "<ul>";
echo "<li>✅ فحص وجود جدول العملاء</li>";
echo "<li>✅ إضافة عمود user_id إذا كان مفقود</li>";
echo "<li>✅ ربط البيانات الموجودة بالمستخدمين</li>";
echo "<li>✅ إضافة فهارس للأداء</li>";
echo "<li>✅ إضافة بيانات تجريبية</li>";
echo "<li>✅ اختبار الاستعلامات المُشكِلة</li>";
echo "</ul>";
echo "</div>";

echo "<h3>اختبار الصفحات:</h3>";
echo "<a href='clients.php' class='btn btn-success' target='_blank'>🔗 صفحة العملاء</a>";
echo "<a href='dashboard.php' class='btn' target='_blank'>🔗 لوحة التحكم</a>";
echo "<a href='create-invoice.php' class='btn' target='_blank'>🔗 إنشاء فاتورة</a>";

echo "<div class='result info'>";
echo "<strong>بيانات الدخول:</strong><br>";
echo "اسم المستخدم: admin<br>";
echo "كلمة المرور: 123456";
echo "</div>";

echo "</body></html>";
?>
