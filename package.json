{"name": "invoice-saas-frontend", "version": "1.0.0", "description": "Frontend assets for Invoice SaaS platform", "main": "assets/js/app.js", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "build:css": "sass assets/scss/main.scss assets/css/compiled.css --style compressed", "watch:css": "sass assets/scss/main.scss assets/css/compiled.css --watch", "lint:js": "eslint assets/js/**/*.js", "lint:css": "stylelint assets/scss/**/*.scss", "fix:js": "eslint assets/js/**/*.js --fix", "fix:css": "stylelint assets/scss/**/*.scss --fix", "optimize:images": "imagemin assets/images/src/* --out-dir=assets/images/optimized", "minify:js": "terser assets/js/app.js -o assets/js/app.min.js", "minify:css": "cleancss -o assets/css/style.min.css assets/css/style.css", "test": "jest", "test:watch": "jest --watch", "serve": "browser-sync start --server --files '*.php, assets/css/*.css, assets/js/*.js'", "clean": "rimraf assets/css/compiled.css assets/js/compiled.js", "prebuild": "npm run clean", "postbuild": "npm run optimize:images"}, "keywords": ["invoice", "saas", "frontend", "javascript", "css", "bootstrap", "arabic", "rtl"], "author": "Your Name <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/invoice-saas.git"}, "bugs": {"url": "https://github.com/your-username/invoice-saas/issues"}, "homepage": "https://github.com/your-username/invoice-saas#readme", "dependencies": {"bootstrap": "^5.3.0", "jquery": "^3.7.0", "chart.js": "^4.3.0", "datatables.net": "^1.13.4", "datatables.net-bs5": "^1.13.4", "select2": "^4.1.0-rc.0", "flatpickr": "^4.6.13", "sweetalert2": "^11.7.12", "toastr": "^2.1.4", "aos": "^2.3.4", "swiper": "^10.0.4", "prismjs": "^1.29.0", "clipboard": "^2.0.11", "moment": "^2.29.4", "lodash": "^4.17.21", "axios": "^1.4.0", "alpinejs": "^3.12.3", "tippy.js": "^6.3.7", "sortablejs": "^1.15.0", "quill": "^1.3.7", "dropzone": "^6.0.0-beta.2", "cropper": "^4.1.0", "fullcalendar": "^6.1.8", "apexcharts": "^3.41.0", "gsap": "^3.12.2"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "babel-loader": "^9.1.2", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "sass": "^1.63.6", "sass-loader": "^13.3.2", "postcss": "^8.4.24", "postcss-loader": "^7.3.3", "autoprefixer": "^10.4.14", "cssnano": "^6.0.1", "mini-css-extract-plugin": "^2.7.6", "html-webpack-plugin": "^5.5.3", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "terser-webpack-plugin": "^5.3.9", "eslint": "^8.43.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "stylelint": "^15.9.0", "stylelint-config-standard": "^33.0.0", "stylelint-config-standard-scss": "^10.0.0", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-svgo": "^10.0.1", "imagemin-webp": "^7.0.0", "clean-css-cli": "^5.6.2", "terser": "^5.19.0", "jest": "^29.5.0", "browser-sync": "^2.29.3", "rimraf": "^5.0.1", "cross-env": "^7.0.3", "npm-run-all": "^4.1.5"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["**/tests/**/*.test.js"], "collectCoverageFrom": ["assets/js/**/*.js", "!assets/js/vendor/**", "!assets/js/**/*.min.js"]}, "eslintConfig": {"extends": ["standard"], "env": {"browser": true, "jquery": true, "es6": true}, "globals": {"bootstrap": "readonly", "Chart": "readonly", "Swal": "readonly", "toastr": "readonly", "AOS": "readonly", "Alpine": "readonly"}, "rules": {"no-console": "warn", "no-debugger": "error", "semi": ["error", "always"], "quotes": ["error", "single"]}}, "stylelint": {"extends": ["stylelint-config-standard", "stylelint-config-standard-scss"], "rules": {"indentation": 2, "string-quotes": "single", "no-duplicate-selectors": true, "color-hex-case": "lower", "color-hex-length": "short", "selector-combinator-space-after": "always", "selector-attribute-quotes": "always", "selector-attribute-operator-space-before": "never", "selector-attribute-operator-space-after": "never", "selector-attribute-brackets-space-inside": "never", "declaration-block-trailing-semicolon": "always", "declaration-colon-space-before": "never", "declaration-colon-space-after": "always", "number-leading-zero": "always", "function-url-quotes": "always", "font-family-name-quotes": "always-where-recommended", "comment-whitespace-inside": "always", "comment-empty-line-before": "always", "at-rule-no-unknown": null, "scss/at-rule-no-unknown": true}}, "babel": {"presets": [["@babel/preset-env", {"targets": {"browsers": ["> 1%", "last 2 versions", "not dead"]}}]]}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}