/**
 * منشئ الفواتير - JavaScript
 * Invoice Creator JavaScript
 */

$(document).ready(function() {
    initializeInvoiceCreator();
});

/**
 * تهيئة منشئ الفواتير
 */
function initializeInvoiceCreator() {
    // معالجة اختيار القالب
    handleTemplateSelection();
    
    // معالجة عناصر الفاتورة
    handleInvoiceItems();
    
    // معالجة العملاء
    handleClientSelection();
    
    // تحديث المعاينة المباشرة
    updateLivePreview();
    
    // ربط الأحداث
    bindEvents();
}

/**
 * معالجة اختيار القالب
 */
function handleTemplateSelection() {
    $('.template-option').on('click', function() {
        $('.template-option').removeClass('selected');
        $(this).addClass('selected');
        
        const templateId = $(this).data('template-id');
        $('#template_id').val(templateId);
        
        updateLivePreview();
    });
}

/**
 * معالجة عناصر الفاتورة
 */
function handleInvoiceItems() {
    // إضافة عنصر جديد
    $('.add-invoice-item').on('click', function() {
        addInvoiceItem();
    });
    
    // حذف عنصر
    $(document).on('click', '.remove-invoice-item', function() {
        if ($('.invoice-item').length > 1) {
            $(this).closest('.invoice-item').remove();
            updateInvoiceTotal();
            updateLivePreview();
        } else {
            alert('يجب أن تحتوي الفاتورة على عنصر واحد على الأقل');
        }
    });
    
    // تحديث المجموع عند تغيير الكمية أو السعر
    $(document).on('input', '.item-quantity, .item-price', function() {
        const item = $(this).closest('.invoice-item');
        updateItemTotal(item);
        updateInvoiceTotal();
        updateLivePreview();
    });
}

/**
 * إضافة عنصر جديد للفاتورة
 */
function addInvoiceItem() {
    const template = $('#invoice-item-template').html();
    const itemCount = $('.invoice-item').length;
    const newItem = template.replace(/\[INDEX\]/g, itemCount);
    
    $('#invoice-items').append(newItem);
    updateInvoiceTotal();
}

/**
 * تحديث مجموع العنصر
 */
function updateItemTotal(item) {
    const quantity = parseFloat(item.find('.item-quantity').val()) || 0;
    const price = parseFloat(item.find('.item-price').val()) || 0;
    const total = quantity * price;
    
    item.find('.item-total').val(total.toFixed(2));
}

/**
 * تحديث مجموع الفاتورة
 */
function updateInvoiceTotal() {
    let subtotal = 0;
    
    // حساب المجموع الفرعي
    $('.invoice-item').each(function() {
        const quantity = parseFloat($(this).find('.item-quantity').val()) || 0;
        const price = parseFloat($(this).find('.item-price').val()) || 0;
        subtotal += quantity * price;
    });
    
    // حساب الضريبة
    const taxRate = parseFloat($('#tax_rate').val()) || 0;
    const taxAmount = subtotal * (taxRate / 100);
    
    // حساب الخصم
    const discountRate = parseFloat($('#discount_rate').val()) || 0;
    const discountAmount = subtotal * (discountRate / 100);
    
    // حساب المجموع النهائي
    const total = subtotal + taxAmount - discountAmount;
    
    // تحديث العرض
    const currency = $('#currency').val() || 'SAR';
    $('#subtotal').text(formatCurrency(subtotal, currency));
    $('#tax-amount').text(formatCurrency(taxAmount, currency));
    $('#discount-amount').text(formatCurrency(discountAmount, currency));
    $('#total-amount').text(formatCurrency(total, currency));
    
    // تحديث الحقول المخفية
    $('#hidden-subtotal').val(subtotal.toFixed(2));
    $('#hidden-tax-amount').val(taxAmount.toFixed(2));
    $('#hidden-discount-amount').val(discountAmount.toFixed(2));
    $('#hidden-total').val(total.toFixed(2));
}

/**
 * معالجة اختيار العميل
 */
function handleClientSelection() {
    $('#client_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const clientData = {
            name: selectedOption.data('name'),
            email: selectedOption.data('email'),
            phone: selectedOption.data('phone'),
            address: selectedOption.data('address')
        };
        
        // تحديث المعاينة مع بيانات العميل
        updateLivePreview();
    });
}

/**
 * حفظ عميل جديد
 */
function saveClient() {
    const formData = {
        name: $('#client_name').val(),
        email: $('#client_email').val(),
        phone: $('#client_phone').val(),
        address: $('#client_address').val()
    };
    
    if (!formData.name.trim()) {
        alert('يرجى إدخال اسم العميل');
        return;
    }
    
    $.ajax({
        url: 'ajax/save-client.php',
        method: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // إضافة العميل الجديد للقائمة
                const newOption = `<option value="${response.client_id}" 
                                          data-name="${formData.name}"
                                          data-email="${formData.email}"
                                          data-phone="${formData.phone}"
                                          data-address="${formData.address}" selected>
                                      ${formData.name}
                                   </option>`;
                $('#client_id').append(newOption);
                
                // إغلاق المودال وإعادة تعيين النموذج
                $('#addClientModal').modal('hide');
                $('#add-client-form')[0].reset();
                
                // تحديث المعاينة
                updateLivePreview();
                
                alert('تم حفظ العميل بنجاح');
            } else {
                alert('حدث خطأ في حفظ العميل: ' + response.message);
            }
        },
        error: function() {
            alert('حدث خطأ في الاتصال بالخادم');
        }
    });
}

/**
 * تحديث المعاينة المباشرة
 */
function updateLivePreview() {
    const formData = $('#invoice-form').serialize();
    
    $.ajax({
        url: 'ajax/live-preview.php',
        method: 'POST',
        data: formData,
        success: function(response) {
            $('#live-preview').html(response);
        },
        error: function() {
            $('#live-preview').html('<div class="alert alert-danger">حدث خطأ في تحديث المعاينة</div>');
        }
    });
}

/**
 * معاينة الفاتورة في مودال
 */
function previewInvoice() {
    const formData = $('#invoice-form').serialize();
    
    $.ajax({
        url: 'ajax/preview-invoice.php',
        method: 'POST',
        data: formData,
        success: function(response) {
            // إنشاء مودال للمعاينة
            const modal = `
                <div class="modal fade" id="invoicePreviewModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">معاينة الفاتورة</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${response}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="printPreview()">
                                    <i class="fas fa-print me-1"></i>طباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // إزالة المودال السابق إن وجد
            $('#invoicePreviewModal').remove();
            
            // إضافة المودال الجديد
            $('body').append(modal);
            
            // إظهار المودال
            $('#invoicePreviewModal').modal('show');
        },
        error: function() {
            alert('حدث خطأ في معاينة الفاتورة');
        }
    });
}

/**
 * طباعة المعاينة
 */
function printPreview() {
    const printContent = $('#invoicePreviewModal .modal-body').html();
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <html>
        <head>
            <title>طباعة الفاتورة</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="assets/css/style.css" rel="stylesheet">
            <style>
                body { font-family: 'Cairo', sans-serif; }
                @media print {
                    .no-print { display: none !important; }
                }
            </style>
        </head>
        <body>
            ${printContent}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);
}

/**
 * ربط الأحداث
 */
function bindEvents() {
    // تحديث المجموع عند تغيير الضريبة أو الخصم
    $('#tax_rate, #discount_rate').on('input', function() {
        updateInvoiceTotal();
        updateLivePreview();
    });
    
    // تحديث المعاينة عند تغيير أي حقل
    $('#invoice-form input, #invoice-form select, #invoice-form textarea').on('input change', function() {
        updateLivePreview();
    });
    
    // تحديث المعاينة عند تغيير العملة
    $('#currency').on('change', function() {
        updateInvoiceTotal();
        updateLivePreview();
    });
    
    // حفظ العميل عند الضغط على Enter في نموذج العميل
    $('#add-client-form input').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            saveClient();
        }
    });
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'SAR') {
    const symbols = {
        'SAR': 'ريال',
        'USD': '$',
        'EUR': '€'
    };
    
    return amount.toFixed(2) + ' ' + (symbols[currency] || currency);
}

/**
 * التحقق من صحة النموذج
 */
function validateForm() {
    let isValid = true;
    const errors = [];
    
    // التحقق من وجود عميل
    if (!$('#client_id').val()) {
        errors.push('يرجى اختيار العميل');
        isValid = false;
    }
    
    // التحقق من وجود عناصر
    if ($('.invoice-item').length === 0) {
        errors.push('يجب إضافة عنصر واحد على الأقل');
        isValid = false;
    }
    
    // التحقق من صحة العناصر
    $('.invoice-item').each(function() {
        const description = $(this).find('input[name*="[description]"]').val();
        const quantity = $(this).find('.item-quantity').val();
        const price = $(this).find('.item-price').val();
        
        if (!description || !quantity || !price) {
            errors.push('يرجى ملء جميع حقول العناصر');
            isValid = false;
            return false;
        }
    });
    
    if (!isValid) {
        alert('يرجى تصحيح الأخطاء التالية:\n' + errors.join('\n'));
    }
    
    return isValid;
}

// تصدير الدوال للاستخدام العام
window.saveClient = saveClient;
window.previewInvoice = previewInvoice;
window.printPreview = printPreview;
