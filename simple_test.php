<?php
// اختبار بسيط جداً
echo "<h1>اختبار بسيط</h1>";
echo "<p>PHP يعمل بشكل صحيح!</p>";
echo "<p>الوقت: " . date('Y-m-d H:i:s') . "</p>";

// اختبار قاعدة البيانات
try {
    $pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '');
    echo "<p>✅ MySQL يعمل</p>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS invoice_saas");
    echo "<p>✅ قاعدة البيانات تم إنشاؤها</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<p><a href='index.php'>اختبار الصفحة الرئيسية</a></p>";
echo "<p><a href='contact.php'>اختبار صفحة التواصل</a></p>";
?>
