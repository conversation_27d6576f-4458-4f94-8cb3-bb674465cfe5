<?php
require_once 'config/config.php';

http_response_code(500);
$page_title = 'خطأ في الخادم - 500';
include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6 text-center">
            <div class="error-page">
                <div class="error-number">500</div>
                <div class="error-icon">
                    <i class="fas fa-server fa-5x text-warning mb-4"></i>
                </div>
                <h2 class="mb-3">خطأ في الخادم</h2>
                <p class="lead mb-4">عذراً، حدث خطأ داخلي في الخادم. نحن نعمل على حل هذه المشكلة.</p>
                
                <div class="error-info">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ما حدث؟</strong><br>
                        واجه الخادم مشكلة غير متوقعة منعته من إكمال طلبك.
                    </div>
                </div>
                
                <div class="error-suggestions">
                    <h6>ما يمكنك فعله:</h6>
                    <div class="row g-3 mt-3">
                        <div class="col-md-6">
                            <div class="suggestion-card">
                                <i class="fas fa-redo text-primary mb-2"></i>
                                <h6>أعد المحاولة</h6>
                                <p>انتظر قليلاً ثم أعد تحميل الصفحة</p>
                                <button onclick="location.reload()" class="btn btn-sm btn-primary">
                                    إعادة تحميل
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="suggestion-card">
                                <i class="fas fa-home text-success mb-2"></i>
                                <h6>العودة للرئيسية</h6>
                                <p>ارجع إلى الصفحة الرئيسية وجرب مرة أخرى</p>
                                <a href="<?php echo APP_URL; ?>" class="btn btn-sm btn-success">
                                    الصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="suggestion-card">
                                <i class="fas fa-clock text-info mb-2"></i>
                                <h6>انتظر قليلاً</h6>
                                <p>قد تكون هناك صيانة مؤقتة على الخادم</p>
                                <span class="text-muted">جرب بعد 5 دقائق</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="suggestion-card">
                                <i class="fas fa-envelope text-danger mb-2"></i>
                                <h6>تواصل معنا</h6>
                                <p>إذا استمرت المشكلة، أخبرنا عنها</p>
                                <a href="<?php echo APP_URL; ?>/contact.php" class="btn btn-sm btn-danger">
                                    الإبلاغ عن المشكلة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="error-details mt-4">
                    <details>
                        <summary class="btn btn-outline-secondary btn-sm">تفاصيل تقنية</summary>
                        <div class="mt-3 p-3 bg-light rounded">
                            <small class="text-muted">
                                <strong>وقت الخطأ:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                                <strong>رقم المرجع:</strong> <?php echo uniqid('ERR-'); ?><br>
                                <strong>عنوان IP:</strong> <?php echo $_SERVER['REMOTE_ADDR'] ?? 'غير معروف'; ?><br>
                                <strong>المتصفح:</strong> <?php echo $_SERVER['HTTP_USER_AGENT'] ?? 'غير معروف'; ?>
                            </small>
                        </div>
                    </details>
                </div>
                
                <div class="status-check mt-4">
                    <p class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        تحقق من حالة الخدمة على 
                        <a href="#" class="text-decoration-none">صفحة الحالة</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 40px 20px;
}

.error-number {
    font-size: 8rem;
    font-weight: bold;
    color: #ffc107;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.error-info {
    margin: 30px 0;
}

.error-suggestions {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin: 30px 0;
}

.suggestion-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    height: 100%;
    transition: transform 0.3s ease;
}

.suggestion-card:hover {
    transform: translateY(-2px);
}

.suggestion-card i {
    font-size: 2rem;
}

.suggestion-card h6 {
    margin: 10px 0;
    font-weight: 600;
}

.suggestion-card p {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 15px;
}

.error-details {
    max-width: 500px;
    margin: 0 auto;
}

.error-details summary {
    cursor: pointer;
    outline: none;
}

@media (max-width: 768px) {
    .error-number {
        font-size: 6rem;
    }
    
    .error-suggestions {
        padding: 20px;
    }
    
    .suggestion-card {
        margin-bottom: 15px;
    }
}

/* Auto refresh countdown */
.refresh-countdown {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
}
</style>

<script>
// Auto refresh after 30 seconds
let countdown = 30;
const countdownElement = document.createElement('div');
countdownElement.className = 'refresh-countdown text-center';
countdownElement.innerHTML = `
    <i class="fas fa-sync-alt me-2"></i>
    <span>سيتم إعادة تحميل الصفحة تلقائياً خلال <strong id="countdown">${countdown}</strong> ثانية</span>
    <button onclick="clearInterval(refreshTimer)" class="btn btn-sm btn-outline-primary ms-2">إلغاء</button>
`;

document.querySelector('.error-page').appendChild(countdownElement);

const refreshTimer = setInterval(() => {
    countdown--;
    document.getElementById('countdown').textContent = countdown;
    
    if (countdown <= 0) {
        location.reload();
    }
}, 1000);

// Log error for debugging (in production, send to logging service)
console.error('Server Error 500 occurred at:', new Date().toISOString());
</script>

<?php include 'includes/footer.php'; ?>
