/**
 * ملف JavaScript الرئيسي
 * Main JavaScript File
 */

$(document).ready(function() {
    // تهيئة التطبيق
    initializeApp();
    
    // معالجة النماذج
    handleForms();
    
    // معالجة الجداول
    handleTables();
    
    // معالجة الفواتير
    handleInvoices();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // إخفاء رسائل التنبيه تلقائياً
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
    
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * معالجة النماذج
 */
function handleForms() {
    // التحقق من صحة النماذج
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
    
    // تنسيق حقول الأرقام
    $('.number-input').on('input', function() {
        this.value = this.value.replace(/[^0-9.]/g, '');
    });
    
    // تنسيق حقول العملة
    $('.currency-input').on('input', function() {
        let value = this.value.replace(/[^0-9.]/g, '');
        if (value) {
            this.value = parseFloat(value).toFixed(2);
        }
    });
}

/**
 * معالجة الجداول
 */
function handleTables() {
    // البحث في الجداول
    $('.table-search').on('keyup', function() {
        let value = $(this).val().toLowerCase();
        let table = $(this).data('table');
        
        $(table + ' tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // ترتيب الجداول
    $('.sortable th').on('click', function() {
        let table = $(this).parents('table').eq(0);
        let rows = table.find('tr:gt(0)').toArray().sort(comparer($(this).index()));
        
        this.asc = !this.asc;
        if (!this.asc) {
            rows = rows.reverse();
        }
        
        for (let i = 0; i < rows.length; i++) {
            table.append(rows[i]);
        }
        
        // تحديث أيقونة الترتيب
        $('.sortable th i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
        $(this).find('i').removeClass('fa-sort').addClass(this.asc ? 'fa-sort-up' : 'fa-sort-down');
    });
}

/**
 * دالة مساعدة للترتيب
 */
function comparer(index) {
    return function(a, b) {
        let valA = getCellValue(a, index);
        let valB = getCellValue(b, index);
        return $.isNumeric(valA) && $.isNumeric(valB) ? valA - valB : valA.toString().localeCompare(valB);
    };
}

function getCellValue(row, index) {
    return $(row).children('td').eq(index).text();
}

/**
 * معالجة الفواتير
 */
function handleInvoices() {
    // إضافة عنصر جديد للفاتورة
    $(document).on('click', '.add-invoice-item', function() {
        let template = $('#invoice-item-template').html();
        let itemsContainer = $('#invoice-items');
        let itemCount = itemsContainer.find('.invoice-item').length;
        
        template = template.replace(/\[INDEX\]/g, itemCount);
        itemsContainer.append(template);
        
        updateInvoiceTotal();
    });
    
    // حذف عنصر من الفاتورة
    $(document).on('click', '.remove-invoice-item', function() {
        $(this).closest('.invoice-item').remove();
        updateInvoiceTotal();
    });
    
    // تحديث المجموع عند تغيير الكمية أو السعر
    $(document).on('input', '.item-quantity, .item-price', function() {
        updateItemTotal($(this).closest('.invoice-item'));
        updateInvoiceTotal();
    });
    
    // تحديث المجموع عند تغيير الضريبة أو الخصم
    $(document).on('input', '#tax-rate, #discount-rate', function() {
        updateInvoiceTotal();
    });
}

/**
 * تحديث مجموع العنصر
 */
function updateItemTotal(item) {
    let quantity = parseFloat(item.find('.item-quantity').val()) || 0;
    let price = parseFloat(item.find('.item-price').val()) || 0;
    let total = quantity * price;
    
    item.find('.item-total').text(total.toFixed(2));
}

/**
 * تحديث مجموع الفاتورة
 */
function updateInvoiceTotal() {
    let subtotal = 0;
    
    // حساب المجموع الفرعي
    $('.invoice-item').each(function() {
        let quantity = parseFloat($(this).find('.item-quantity').val()) || 0;
        let price = parseFloat($(this).find('.item-price').val()) || 0;
        subtotal += quantity * price;
    });
    
    // حساب الضريبة
    let taxRate = parseFloat($('#tax-rate').val()) || 0;
    let taxAmount = subtotal * (taxRate / 100);
    
    // حساب الخصم
    let discountRate = parseFloat($('#discount-rate').val()) || 0;
    let discountAmount = subtotal * (discountRate / 100);
    
    // حساب المجموع النهائي
    let total = subtotal + taxAmount - discountAmount;
    
    // تحديث العرض
    $('#subtotal').text(subtotal.toFixed(2));
    $('#tax-amount').text(taxAmount.toFixed(2));
    $('#discount-amount').text(discountAmount.toFixed(2));
    $('#total-amount').text(total.toFixed(2));
    
    // تحديث الحقول المخفية
    $('#hidden-subtotal').val(subtotal.toFixed(2));
    $('#hidden-tax-amount').val(taxAmount.toFixed(2));
    $('#hidden-discount-amount').val(discountAmount.toFixed(2));
    $('#hidden-total').val(total.toFixed(2));
}

/**
 * معاينة الفاتورة
 */
function previewInvoice() {
    let formData = $('#invoice-form').serialize();
    
    $.ajax({
        url: 'ajax/preview-invoice.php',
        method: 'POST',
        data: formData,
        success: function(response) {
            $('#invoice-preview').html(response);
            $('#preview-modal').modal('show');
        },
        error: function() {
            alert('حدث خطأ في معاينة الفاتورة');
        }
    });
}

/**
 * طباعة الفاتورة
 */
function printInvoice(invoiceId) {
    window.open('print-invoice.php?id=' + invoiceId, '_blank');
}

/**
 * تصدير الفاتورة إلى PDF
 */
function exportToPDF(invoiceId) {
    window.location.href = 'export-pdf.php?id=' + invoiceId;
}

/**
 * إرسال الفاتورة بالبريد الإلكتروني
 */
function sendInvoiceEmail(invoiceId) {
    if (confirm('هل تريد إرسال الفاتورة بالبريد الإلكتروني؟')) {
        $.ajax({
            url: 'ajax/send-invoice.php',
            method: 'POST',
            data: { invoice_id: invoiceId },
            success: function(response) {
                let result = JSON.parse(response);
                if (result.success) {
                    alert('تم إرسال الفاتورة بنجاح');
                } else {
                    alert('حدث خطأ في إرسال الفاتورة: ' + result.message);
                }
            },
            error: function() {
                alert('حدث خطأ في إرسال الفاتورة');
            }
        });
    }
}

/**
 * حذف الفاتورة
 */
function deleteInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        $.ajax({
            url: 'ajax/delete-invoice.php',
            method: 'POST',
            data: { invoice_id: invoiceId },
            success: function(response) {
                let result = JSON.parse(response);
                if (result.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ في حذف الفاتورة: ' + result.message);
                }
            },
            error: function() {
                alert('حدث خطأ في حذف الفاتورة');
            }
        });
    }
}

/**
 * تحديث حالة الفاتورة
 */
function updateInvoiceStatus(invoiceId, status) {
    $.ajax({
        url: 'ajax/update-invoice-status.php',
        method: 'POST',
        data: { 
            invoice_id: invoiceId,
            status: status
        },
        success: function(response) {
            let result = JSON.parse(response);
            if (result.success) {
                location.reload();
            } else {
                alert('حدث خطأ في تحديث حالة الفاتورة: ' + result.message);
            }
        },
        error: function() {
            alert('حدث خطأ في تحديث حالة الفاتورة');
        }
    });
}

/**
 * تحميل البيانات بـ AJAX
 */
function loadData(url, container, callback) {
    $(container).html('<div class="loading"><div class="spinner-border" role="status"></div></div>');
    
    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            $(container).html(response);
            if (callback) callback();
        },
        error: function() {
            $(container).html('<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>');
        }
    });
}
