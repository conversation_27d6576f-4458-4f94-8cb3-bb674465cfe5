<?php
require_once 'config/config.php';

$page_title = 'مركز المساعدة';
include 'includes/header.php';
?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold mb-3">مركز المساعدة</h1>
            <p class="lead">كل ما تحتاج لمعرفته عن استخدام منصة إنشاء الفواتير</p>
        </div>
    </div>
    
    <!-- Search Box -->
    <div class="row mb-5">
        <div class="col-lg-6 mx-auto">
            <div class="search-box">
                <div class="input-group input-group-lg">
                    <input type="text" class="form-control" placeholder="ابحث في المساعدة..." id="helpSearch">
                    <button class="btn btn-primary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Links -->
    <div class="row g-4 mb-5">
        <div class="col-lg-3 col-md-6">
            <div class="help-card">
                <div class="help-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h5>البدء السريع</h5>
                <p>تعلم كيفية إنشاء أول فاتورة لك في دقائق</p>
                <a href="#getting-started" class="btn btn-outline-primary btn-sm">ابدأ الآن</a>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="help-card">
                <div class="help-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <h5>إدارة الفواتير</h5>
                <p>كيفية إنشاء وتحرير وإرسال الفواتير</p>
                <a href="#invoice-management" class="btn btn-outline-primary btn-sm">تعلم المزيد</a>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="help-card">
                <div class="help-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h5>إدارة العملاء</h5>
                <p>إضافة وتنظيم بيانات عملائك</p>
                <a href="#client-management" class="btn btn-outline-primary btn-sm">اقرأ المزيد</a>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="help-card">
                <div class="help-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h5>التقارير</h5>
                <p>فهم وتحليل تقارير أعمالك</p>
                <a href="#reports" class="btn btn-outline-primary btn-sm">استكشف</a>
            </div>
        </div>
    </div>
    
    <!-- FAQ Sections -->
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">الأسئلة الشائعة</h2>
            
            <!-- Getting Started -->
            <div class="faq-section" id="getting-started">
                <h3 class="section-title">
                    <i class="fas fa-rocket me-2"></i>البدء السريع
                </h3>
                
                <div class="accordion mb-4" id="gettingStartedAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#gs1">
                                كيف أنشئ حساب جديد؟
                            </button>
                        </h2>
                        <div id="gs1" class="accordion-collapse collapse show" data-bs-parent="#gettingStartedAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>انقر على زر "إنشاء حساب" في الصفحة الرئيسية</li>
                                    <li>املأ البيانات المطلوبة (الاسم، البريد الإلكتروني، كلمة المرور)</li>
                                    <li>اختر اسم شركتك (اختياري)</li>
                                    <li>وافق على شروط الاستخدام</li>
                                    <li>انقر على "إنشاء الحساب"</li>
                                </ol>
                                <p>ستحصل على حساب مجاني يتيح لك إنشاء 5 فواتير شهرياً.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gs2">
                                كيف أنشئ أول فاتورة؟
                            </button>
                        </h2>
                        <div id="gs2" class="accordion-collapse collapse" data-bs-parent="#gettingStartedAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>سجل دخولك إلى لوحة التحكم</li>
                                    <li>انقر على "إنشاء فاتورة جديدة"</li>
                                    <li>اختر القالب المناسب</li>
                                    <li>أضف بيانات العميل</li>
                                    <li>أضف عناصر الفاتورة (الخدمات/المنتجات)</li>
                                    <li>راجع المعاينة واحفظ الفاتورة</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gs3">
                                ما هي القوالب المتاحة؟
                            </button>
                        </h2>
                        <div id="gs3" class="accordion-collapse collapse" data-bs-parent="#gettingStartedAccordion">
                            <div class="accordion-body">
                                نوفر مجموعة متنوعة من القوالب:
                                <ul>
                                    <li><strong>الكلاسيكي:</strong> تصميم بسيط ومهني</li>
                                    <li><strong>الحديث:</strong> تصميم عصري وأنيق</li>
                                    <li><strong>الإبداعي:</strong> تصميم ملون ومميز</li>
                                    <li><strong>البسيط:</strong> تصميم نظيف وواضح</li>
                                    <li><strong>المهني:</strong> تصميم رسمي للشركات</li>
                                </ul>
                                <p>يمكنك معاينة جميع القوالب في صفحة "القوالب".</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Invoice Management -->
            <div class="faq-section" id="invoice-management">
                <h3 class="section-title">
                    <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                </h3>
                
                <div class="accordion mb-4" id="invoiceAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#inv1">
                                كيف أحرر فاتورة موجودة؟
                            </button>
                        </h2>
                        <div id="inv1" class="accordion-collapse collapse" data-bs-parent="#invoiceAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>اذهب إلى صفحة "الفواتير"</li>
                                    <li>ابحث عن الفاتورة المطلوبة</li>
                                    <li>انقر على أيقونة "تحرير" بجانب الفاتورة</li>
                                    <li>قم بالتعديلات المطلوبة</li>
                                    <li>احفظ التغييرات</li>
                                </ol>
                                <p><strong>ملاحظة:</strong> لا يمكن تحرير الفواتير المدفوعة.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#inv2">
                                كيف أرسل فاتورة للعميل؟
                            </button>
                        </h2>
                        <div id="inv2" class="accordion-collapse collapse" data-bs-parent="#invoiceAccordion">
                            <div class="accordion-body">
                                يمكنك إرسال الفاتورة بعدة طرق:
                                <ul>
                                    <li><strong>البريد الإلكتروني:</strong> انقر على "إرسال بالبريد" وسيتم إرسالها تلقائياً</li>
                                    <li><strong>تحميل PDF:</strong> انقر على "تحميل PDF" وأرسلها بنفسك</li>
                                    <li><strong>الطباعة:</strong> انقر على "طباعة" لطباعة نسخة ورقية</li>
                                    <li><strong>الرابط المباشر:</strong> شارك رابط الفاتورة مع العميل</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#inv3">
                                كيف أتتبع حالة الفواتير؟
                            </button>
                        </h2>
                        <div id="inv3" class="accordion-collapse collapse" data-bs-parent="#invoiceAccordion">
                            <div class="accordion-body">
                                حالات الفواتير:
                                <ul>
                                    <li><span class="badge bg-secondary">مسودة</span> - فاتورة لم يتم إرسالها بعد</li>
                                    <li><span class="badge bg-warning">مرسلة</span> - فاتورة تم إرسالها وفي انتظار الدفع</li>
                                    <li><span class="badge bg-success">مدفوعة</span> - فاتورة تم دفعها</li>
                                    <li><span class="badge bg-danger">متأخرة</span> - فاتورة تجاوزت تاريخ الاستحقاق</li>
                                    <li><span class="badge bg-dark">ملغاة</span> - فاتورة تم إلغاؤها</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Client Management -->
            <div class="faq-section" id="client-management">
                <h3 class="section-title">
                    <i class="fas fa-users me-2"></i>إدارة العملاء
                </h3>
                
                <div class="accordion mb-4" id="clientAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#cl1">
                                كيف أضيف عميل جديد؟
                            </button>
                        </h2>
                        <div id="cl1" class="accordion-collapse collapse" data-bs-parent="#clientAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>اذهب إلى صفحة "العملاء"</li>
                                    <li>انقر على "إضافة عميل جديد"</li>
                                    <li>املأ بيانات العميل (الاسم مطلوب)</li>
                                    <li>أضف معلومات إضافية مثل البريد الإلكتروني والهاتف</li>
                                    <li>احفظ البيانات</li>
                                </ol>
                                <p>يمكنك أيضاً إضافة عميل جديد أثناء إنشاء فاتورة.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#cl2">
                                كيف أحرر بيانات عميل؟
                            </button>
                        </h2>
                        <div id="cl2" class="accordion-collapse collapse" data-bs-parent="#clientAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>اذهب إلى صفحة "العملاء"</li>
                                    <li>ابحث عن العميل المطلوب</li>
                                    <li>انقر على القائمة المنسدلة بجانب اسم العميل</li>
                                    <li>اختر "تحرير"</li>
                                    <li>قم بالتعديلات واحفظ</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Reports -->
            <div class="faq-section" id="reports">
                <h3 class="section-title">
                    <i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات
                </h3>
                
                <div class="accordion mb-4" id="reportsAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#rep1">
                                ما هي التقارير المتاحة؟
                            </button>
                        </h2>
                        <div id="rep1" class="accordion-collapse collapse" data-bs-parent="#reportsAccordion">
                            <div class="accordion-body">
                                التقارير المتاحة:
                                <ul>
                                    <li><strong>تقرير الإيرادات:</strong> إجمالي المبالغ المحصلة</li>
                                    <li><strong>تقرير الفواتير:</strong> عدد وحالة الفواتير</li>
                                    <li><strong>تقرير العملاء:</strong> أفضل العملاء وأكثرهم تعاملاً</li>
                                    <li><strong>تقرير الفواتير المتأخرة:</strong> الفواتير التي تجاوزت موعد الاستحقاق</li>
                                    <li><strong>الرسوم البيانية:</strong> تمثيل مرئي للبيانات</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#rep2">
                                كيف أصدر التقارير؟
                            </button>
                        </h2>
                        <div id="rep2" class="accordion-collapse collapse" data-bs-parent="#reportsAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>اذهب إلى صفحة "التقارير"</li>
                                    <li>اختر الفترة الزمنية المطلوبة</li>
                                    <li>حدد نوع التقرير</li>
                                    <li>انقر على "تحديث التقرير"</li>
                                    <li>يمكنك تصدير التقرير أو طباعته</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contact Support -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="support-section text-center">
                <h3>لم تجد ما تبحث عنه؟</h3>
                <p class="lead">فريق الدعم جاهز لمساعدتك</p>
                <div class="support-options">
                    <a href="contact.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-envelope me-2"></i>تواصل معنا
                    </a>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-at me-2"></i><EMAIL>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.search-box {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.help-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.help-card:hover {
    transform: translateY(-5px);
}

.help-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.help-card h5 {
    margin-bottom: 15px;
    font-weight: 600;
}

.help-card p {
    color: #6c757d;
    margin-bottom: 20px;
}

.faq-section {
    margin-bottom: 40px;
}

.section-title {
    color: #007bff;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.accordion-item {
    border: none;
    margin-bottom: 10px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.accordion-button {
    background: #f8f9fa;
    border: none;
    font-weight: 500;
    padding: 20px;
    color: #333;
}

.accordion-button:not(.collapsed) {
    background: #007bff;
    color: white;
}

.accordion-button:focus {
    box-shadow: none;
}

.accordion-body {
    padding: 25px;
    background: white;
}

.accordion-body ol,
.accordion-body ul {
    margin-bottom: 15px;
}

.accordion-body li {
    margin-bottom: 8px;
}

.support-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    padding: 50px 30px;
    margin-top: 40px;
}

.support-options {
    margin-top: 30px;
}

.badge {
    font-size: 0.8rem;
    padding: 4px 8px;
}

@media (max-width: 768px) {
    .help-card {
        margin-bottom: 20px;
    }

    .support-options {
        display: flex;
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .support-options .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>

<script>
// البحث في المساعدة
document.getElementById('helpSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const accordionItems = document.querySelectorAll('.accordion-item');

    accordionItems.forEach(item => {
        const button = item.querySelector('.accordion-button');
        const body = item.querySelector('.accordion-body');
        const buttonText = button.textContent.toLowerCase();
        const bodyText = body.textContent.toLowerCase();

        if (buttonText.includes(searchTerm) || bodyText.includes(searchTerm)) {
            item.style.display = 'block';

            // إبراز النص المطابق
            if (searchTerm.length > 2) {
                highlightText(button, searchTerm);
                highlightText(body, searchTerm);
            }
        } else {
            item.style.display = 'none';
        }
    });

    // إظهار رسالة عدم وجود نتائج
    const visibleItems = document.querySelectorAll('.accordion-item[style="display: block"], .accordion-item:not([style])');
    const noResults = document.getElementById('noResults');

    if (visibleItems.length === 0 && searchTerm.length > 0) {
        if (!noResults) {
            const message = document.createElement('div');
            message.id = 'noResults';
            message.className = 'alert alert-info text-center';
            message.innerHTML = '<i class="fas fa-search me-2"></i>لم يتم العثور على نتائج مطابقة لبحثك';
            document.querySelector('.faq-section').appendChild(message);
        }
    } else if (noResults) {
        noResults.remove();
    }
});

// دالة لإبراز النص
function highlightText(element, searchTerm) {
    if (searchTerm.length < 3) return;

    const originalText = element.textContent;
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const highlightedText = originalText.replace(regex, '<mark>$1</mark>');

    if (highlightedText !== originalText) {
        element.innerHTML = highlightedText;
    }
}

// التمرير السلس للأقسام
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// تأثيرات الحركة عند التمرير
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('fade-in');
        }
    });
}, observerOptions);

// مراقبة العناصر
document.querySelectorAll('.help-card, .faq-section').forEach(el => {
    observer.observe(el);
});

// إضافة تأثير الحركة
const style = document.createElement('style');
style.textContent = `
    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    mark {
        background-color: #fff3cd;
        padding: 2px 4px;
        border-radius: 3px;
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>
