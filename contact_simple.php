<?php
// إصدار مبسط من صفحة التواصل للاختبار
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>اختبار صفحة التواصل</h1>";

// اختبار تحميل ملف التكوين
try {
    echo "<h2>1. اختبار تحميل التكوين</h2>";
    require_once 'config/config.php';
    echo "✅ تم تحميل ملف التكوين بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل التكوين: " . $e->getMessage() . "<br>";
    exit;
}

// اختبار قاعدة البيانات
try {
    echo "<h2>2. اختبار قاعدة البيانات</h2>";
    $pdo = getDBConnection();
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    
    // التحقق من وجود جدول contact_messages
    $stmt = $pdo->query("SHOW TABLES LIKE 'contact_messages'");
    if ($stmt->rowCount() > 0) {
        echo "✅ جدول contact_messages موجود<br>";
    } else {
        echo "❌ جدول contact_messages غير موجود - سيتم إنشاؤه<br>";
        
        // إنشاء الجدول
        $sql = "
        CREATE TABLE contact_messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('general', 'support', 'billing', 'feature', 'bug', 'partnership') DEFAULT 'general',
            status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "✅ تم إنشاء جدول contact_messages<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار معالجة النموذج
$success = '';
$errors = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h2>3. معالجة النموذج</h2>";
    
    try {
        $name = sanitize($_POST['name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $subject = sanitize($_POST['subject'] ?? '');
        $message = sanitize($_POST['message'] ?? '');
        $type = sanitize($_POST['type'] ?? 'general');
        
        echo "البيانات المستلمة:<br>";
        echo "الاسم: $name<br>";
        echo "البريد: $email<br>";
        echo "الموضوع: $subject<br>";
        echo "النوع: $type<br>";
        
        // التحقق من البيانات
        if (empty($name)) {
            $errors[] = 'يرجى إدخال الاسم';
        }
        
        if (empty($email)) {
            $errors[] = 'يرجى إدخال البريد الإلكتروني';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (empty($subject)) {
            $errors[] = 'يرجى إدخال الموضوع';
        }
        
        if (empty($message)) {
            $errors[] = 'يرجى إدخال الرسالة';
        }
        
        // حفظ الرسالة
        if (empty($errors)) {
            $pdo = getDBConnection();
            $stmt = $pdo->prepare("
                INSERT INTO contact_messages (name, email, subject, message, type, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            if ($stmt->execute([$name, $email, $subject, $message, $type, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']])) {
                $success = 'تم إرسال رسالتك بنجاح!';
                echo "✅ $success<br>";
            } else {
                $errors[] = 'حدث خطأ في إرسال الرسالة';
                echo "❌ خطأ في الحفظ<br>";
            }
        }
        
        if (!empty($errors)) {
            echo "❌ أخطاء:<br>";
            foreach ($errors as $error) {
                echo "- $error<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في معالجة النموذج: " . $e->getMessage() . "<br>";
    }
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة التواصل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 800px; margin: 20px auto; }
        .alert { margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>نموذج التواصل المبسط</h1>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <?php echo $success; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" class="row g-3">
            <div class="col-md-6">
                <label for="name" class="form-label">الاسم الكامل *</label>
                <input type="text" class="form-control" id="name" name="name" 
                       value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                       required>
            </div>
            
            <div class="col-md-6">
                <label for="email" class="form-label">البريد الإلكتروني *</label>
                <input type="email" class="form-control" id="email" name="email" 
                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                       required>
            </div>
            
            <div class="col-md-6">
                <label for="type" class="form-label">نوع الاستفسار</label>
                <select class="form-select" id="type" name="type">
                    <option value="general">استفسار عام</option>
                    <option value="support">دعم فني</option>
                    <option value="billing">الفوترة والاشتراك</option>
                    <option value="feature">طلب ميزة جديدة</option>
                    <option value="bug">الإبلاغ عن مشكلة</option>
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="subject" class="form-label">الموضوع *</label>
                <input type="text" class="form-control" id="subject" name="subject" 
                       value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>" 
                       required>
            </div>
            
            <div class="col-12">
                <label for="message" class="form-label">الرسالة *</label>
                <textarea class="form-control" id="message" name="message" rows="5" 
                          required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
            </div>
            
            <div class="col-12">
                <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                <a href="index.php" class="btn btn-secondary">العودة للرئيسية</a>
            </div>
        </form>
        
        <hr>
        <h3>روابط مفيدة:</h3>
        <ul>
            <li><a href="test.php">اختبار النظام</a></li>
            <li><a href="create_database.php">إنشاء قاعدة البيانات</a></li>
            <li><a href="debug.php">تشخيص مفصل</a></li>
            <li><a href="contact.php">صفحة التواصل الأصلية</a></li>
        </ul>
    </div>
</body>
</html>
