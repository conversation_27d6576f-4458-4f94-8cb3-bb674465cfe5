# Invoice SaaS - Git Ignore File

# Configuration files
config/config.php
.env
.env.local
.env.production

# Database
*.sql
*.sqlite
*.db

# Logs
logs/
*.log
error_log
access_log

# Cache
cache/
tmp/
temp/
*.cache

# Uploads and user files
assets/uploads/*
!assets/uploads/.gitkeep
assets/temp/*
!assets/temp/.gitkeep

# Backups
backups/
*.backup
*.bak

# Vendor directories
vendor/
node_modules/

# Composer
composer.lock
composer.phar

# NPM
package-lock.json
yarn.lock

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build and compiled files
assets/css/compiled.css
assets/js/compiled.js
assets/css/*.min.css
assets/js/*.min.js
dist/
build/

# Coverage and test reports
coverage/
.nyc_output/
.coverage
phpunit.xml
.phpunit.result.cache

# Documentation
docs/api/
docs/coverage/

# Security
*.pem
*.key
*.crt
*.p12
*.pfx

# Session files
sessions/
*.sess

# Error pages (if generated)
50x.html

# Temporary files
*.tmp
*.temp

# Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Image optimization
assets/images/src/
assets/images/optimized/

# Development tools
.sass-cache/
.grunt/
.bower_components/

# PHP specific
*.php~
*.php.bak

# Apache/Nginx
.htaccess.bak
nginx.conf.bak

# SSL certificates
ssl/
certificates/

# Email templates (if generated)
email_templates/compiled/

# Reports (if generated)
reports/generated/

# Maintenance mode
maintenance.flag

# Local development
local/
dev/

# Third-party integrations
integrations/cache/
integrations/logs/

# API documentation
api-docs/

# Webpack
webpack-stats.json

# ESLint
.eslintcache

# Stylelint
.stylelintcache

# Jest
jest.config.js

# Babel
.babelrc

# PostCSS
postcss.config.js

# Browserslist
.browserslistrc

# EditorConfig
.editorconfig

# Git hooks
.git/hooks/pre-commit.sample
.git/hooks/pre-push.sample

# Local environment variables
.env.example.local

# Database migrations (if auto-generated)
migrations/auto/

# Localization (if compiled)
locale/compiled/

# Analytics data
analytics/

# Performance monitoring
performance/

# A/B testing data
experiments/

# Feature flags
features/

# Monitoring and alerting
monitoring/
alerts/

# Deployment scripts (sensitive)
deploy/production/
deploy/staging/

# Docker (if used)
docker-compose.override.yml
.dockerignore

# Kubernetes (if used)
k8s/secrets/

# Terraform (if used)
*.tfstate
*.tfstate.backup
.terraform/

# AWS (if used)
.aws/

# Google Cloud (if used)
.gcloud/

# Azure (if used)
.azure/

# Vagrant (if used)
.vagrant/

# VirtualBox (if used)
*.vbox
*.vdi

# VMware (if used)
*.vmx
*.vmdk

# Parallels (if used)
*.pvm

# XAMPP/WAMP specific
xampp/
wamp/

# Local server configurations
apache/
nginx/
php/

# Database dumps
dumps/
*.dump

# Profiling data
profiling/
*.prof

# Debugging
debug/
*.debug

# Testing
tests/output/
tests/reports/

# Benchmarking
benchmarks/results/

# Load testing
load-tests/results/

# Security scanning
security-scans/

# Code quality reports
quality-reports/

# Dependency scanning
dependency-scans/

# License scanning
license-scans/

# Vulnerability reports
vulnerability-reports/

# Compliance reports
compliance-reports/

# Audit logs (sensitive)
audit-logs/sensitive/

# Financial data (sensitive)
financial/sensitive/

# Customer data (sensitive)
customers/sensitive/

# Payment data (sensitive)
payments/sensitive/

# Personal data (sensitive)
personal/sensitive/

# Legal documents (sensitive)
legal/sensitive/

# Contracts (sensitive)
contracts/sensitive/

# Internal documents (sensitive)
internal/sensitive/

# HR data (sensitive)
hr/sensitive/

# Accounting data (sensitive)
accounting/sensitive/

# Tax data (sensitive)
tax/sensitive/

# Insurance data (sensitive)
insurance/sensitive/

# Banking data (sensitive)
banking/sensitive/

# Investment data (sensitive)
investments/sensitive/

# Intellectual property (sensitive)
ip/sensitive/

# Trade secrets (sensitive)
trade-secrets/sensitive/

# Confidential data (sensitive)
confidential/sensitive/

# Proprietary data (sensitive)
proprietary/sensitive/

# Classified data (sensitive)
classified/sensitive/

# Restricted data (sensitive)
restricted/sensitive/

# Private data (sensitive)
private/sensitive/

# Secret data (sensitive)
secret/sensitive/

# Top secret data (sensitive)
top-secret/sensitive/