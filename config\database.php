<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'invoice_saas';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;

    public function connect() {
        if ($this->conn !== null) {
            return $this->conn;
        }

        try {
            // محاولة الاتصال بقاعدة البيانات مباشرة
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}",
                PDO::ATTR_TIMEOUT => 30
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);

        } catch(PDOException $e) {
            // إذا فشل الاتصال، حاول إنشاء قاعدة البيانات
            try {
                $dsn_create = "mysql:host=" . $this->host . ";charset=" . $this->charset;
                $pdo_create = new PDO($dsn_create, $this->username, $this->password);
                $pdo_create->exec("CREATE DATABASE IF NOT EXISTS `{$this->db_name}` CHARACTER SET {$this->charset} COLLATE {$this->charset}_unicode_ci");

                // محاولة الاتصال مرة أخرى
                $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
                $this->conn = new PDO($dsn, $this->username, $this->password, $options);

                // إنشاء الجداول الأساسية
                $this->createBasicTables();

            } catch(PDOException $e2) {
                throw new Exception('Database Connection Error: ' . $e2->getMessage());
            }
        }

        return $this->conn;
    }

    private function createBasicTables() {
        $tables = [
            'users' => "
            CREATE TABLE IF NOT EXISTS users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                company_name VARCHAR(100),
                phone VARCHAR(20),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50),
                subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
                subscription_expires_at DATETIME NULL,
                is_active BOOLEAN DEFAULT TRUE,
                email_verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_subscription (subscription_plan)
            ) ENGINE=InnoDB DEFAULT CHARSET={$this->charset} COLLATE={$this->charset}_unicode_ci",

            'contact_messages' => "
            CREATE TABLE IF NOT EXISTS contact_messages (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(255) NOT NULL,
                subject VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type ENUM('general', 'support', 'billing', 'feature', 'bug', 'partnership') DEFAULT 'general',
                status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_type (type),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET={$this->charset} COLLATE={$this->charset}_unicode_ci"
        ];

        foreach ($tables as $tableName => $sql) {
            try {
                $this->conn->exec($sql);
            } catch (PDOException $e) {
                // تجاهل الأخطاء إذا كان الجدول موجوداً بالفعل
                if (strpos($e->getMessage(), 'already exists') === false) {
                    error_log("Error creating table $tableName: " . $e->getMessage());
                }
            }
        }

        // إنشاء مستخدم تجريبي إذا لم يكن موجوداً
        try {
            $stmt = $this->conn->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
            $stmt->execute();
            if ($stmt->fetchColumn() == 0) {
                $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
                $stmt = $this->conn->prepare("
                    INSERT INTO users (username, email, password, first_name, last_name, company_name)
                    VALUES ('admin', '<EMAIL>', ?, 'المدير', 'العام', 'شركة تجريبية')
                ");
                $stmt->execute([$hashedPassword]);
            }
        } catch (PDOException $e) {
            error_log("Error creating admin user: " . $e->getMessage());
        }
    }
}
?>
