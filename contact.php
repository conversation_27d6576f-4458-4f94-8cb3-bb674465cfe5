<?php
// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// دالة تنظيف البيانات البسيطة
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// دالة الاتصال بقاعدة البيانات
function getDBConnection() {
    try {
        $host = 'localhost';
        $dbname = 'invoice_saas';
        $username = 'root';
        $password = '';

        $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

        return $pdo;
    } catch (PDOException $e) {
        throw new Exception("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
    }
}

$success = '';
$errors = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $message = sanitize($_POST['message'] ?? '');
    $type = sanitize($_POST['type'] ?? 'general');

    // التحقق من البيانات
    if (empty($name)) {
        $errors[] = 'يرجى إدخال الاسم';
    }

    if (empty($email)) {
        $errors[] = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }

    if (empty($subject)) {
        $errors[] = 'يرجى إدخال الموضوع';
    }

    if (empty($message)) {
        $errors[] = 'يرجى إدخال الرسالة';
    }

    // حفظ الرسالة في قاعدة البيانات
    if (empty($errors)) {
        try {
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            $pdo_create = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
            $pdo_create->exec("CREATE DATABASE IF NOT EXISTS invoice_saas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

            $pdo = getDBConnection();

            // إنشاء الجدول إذا لم يكن موجوداً
            $createTable = "
            CREATE TABLE IF NOT EXISTS contact_messages (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(255) NOT NULL,
                subject VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type ENUM('general', 'support', 'billing', 'feature', 'bug', 'partnership') DEFAULT 'general',
                status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_type (type),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $pdo->exec($createTable);

            $stmt = $pdo->prepare("
                INSERT INTO contact_messages (name, email, subject, message, type, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            $ip = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            if ($stmt->execute([$name, $email, $subject, $message, $type, $ip, $userAgent])) {
                $success = 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.';
                // إعادة تعيين النموذج
                $_POST = [];
            } else {
                $errors[] = 'حدث خطأ في إرسال الرسالة، يرجى المحاولة لاحقاً';
            }
        } catch (Exception $e) {
            $errors[] = 'حدث خطأ في النظام: ' . $e->getMessage();
        }
    }
}

$page_title = 'تواصل معنا';
include 'includes/header.php';
?>

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold mb-3">تواصل معنا</h1>
            <p class="lead">نحن هنا لمساعدتك! تواصل معنا وسنرد عليك في أقرب وقت</p>
        </div>
    </div>
    
    <div class="row g-5">
        <!-- Contact Form -->
        <div class="col-lg-8">
            <div class="contact-form-card">
                <h3 class="mb-4">أرسل لنا رسالة</h3>
                
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                                   required>
                            <div class="invalid-feedback">
                                يرجى إدخال الاسم
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                   required>
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">نوع الاستفسار</label>
                            <select class="form-select" id="type" name="type">
                                <option value="general" <?php echo (isset($_POST['type']) && $_POST['type'] === 'general') ? 'selected' : ''; ?>>استفسار عام</option>
                                <option value="support" <?php echo (isset($_POST['type']) && $_POST['type'] === 'support') ? 'selected' : ''; ?>>دعم فني</option>
                                <option value="billing" <?php echo (isset($_POST['type']) && $_POST['type'] === 'billing') ? 'selected' : ''; ?>>الفوترة والاشتراك</option>
                                <option value="feature" <?php echo (isset($_POST['type']) && $_POST['type'] === 'feature') ? 'selected' : ''; ?>>طلب ميزة جديدة</option>
                                <option value="bug" <?php echo (isset($_POST['type']) && $_POST['type'] === 'bug') ? 'selected' : ''; ?>>الإبلاغ عن مشكلة</option>
                                <option value="partnership" <?php echo (isset($_POST['type']) && $_POST['type'] === 'partnership') ? 'selected' : ''; ?>>شراكة تجارية</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="subject" class="form-label">الموضوع *</label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>" 
                                   required>
                            <div class="invalid-feedback">
                                يرجى إدخال الموضوع
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">الرسالة *</label>
                        <textarea class="form-control" id="message" name="message" rows="6" 
                                  placeholder="اكتب رسالتك هنا..." required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                        <div class="invalid-feedback">
                            يرجى إدخال الرسالة
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-paper-plane me-2"></i>إرسال الرسالة
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Contact Info -->
        <div class="col-lg-4">
            <div class="contact-info">
                <h3 class="mb-4">معلومات التواصل</h3>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-details">
                        <h5>البريد الإلكتروني</h5>
                        <p><EMAIL></p>
                        <p><EMAIL></p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="contact-details">
                        <h5>الهاتف</h5>
                        <p>+966 11 123 4567</p>
                        <p>+966 50 123 4567</p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="contact-details">
                        <h5>العنوان</h5>
                        <p>الرياض، المملكة العربية السعودية<br>
                        حي الملك فهد، طريق الملك فهد<br>
                        مبنى الأعمال، الطابق الخامس</p>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="contact-details">
                        <h5>ساعات العمل</h5>
                        <p>الأحد - الخميس: 9:00 ص - 6:00 م<br>
                        الجمعة - السبت: مغلق</p>
                    </div>
                </div>
            </div>
            
            <!-- Social Media -->
            <div class="social-media mt-4">
                <h5 class="mb-3">تابعنا على</h5>
                <div class="social-links">
                    <a href="#" class="social-link facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-link twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-link linkedin">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="social-link instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="social-link youtube">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- FAQ Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="faq-section">
                <h3 class="text-center mb-4">أسئلة شائعة قبل التواصل</h3>
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="faq-item">
                            <h6><i class="fas fa-question-circle me-2 text-primary"></i>كم يستغرق الرد على الاستفسارات؟</h6>
                            <p>نرد على جميع الاستفسارات خلال 24 ساعة في أيام العمل.</p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="faq-item">
                            <h6><i class="fas fa-question-circle me-2 text-primary"></i>هل يمكنني طلب ميزة جديدة؟</h6>
                            <p>نعم، نرحب بجميع الاقتراحات ونأخذها بعين الاعتبار في التطوير.</p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="faq-item">
                            <h6><i class="fas fa-question-circle me-2 text-primary"></i>هل تقدمون دعم فني مجاني؟</h6>
                            <p>نعم، نقدم دعم فني مجاني لجميع المستخدمين عبر البريد الإلكتروني.</p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="faq-item">
                            <h6><i class="fas fa-question-circle me-2 text-primary"></i>كيف يمكنني الإبلاغ عن مشكلة؟</h6>
                            <p>يمكنك الإبلاغ عن المشاكل عبر نموذج التواصل أو البريد الإلكتروني.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-form-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-info {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-left: 20px;
    flex-shrink: 0;
}

.contact-details h5 {
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
}

.contact-details p {
    margin: 0;
    color: #6c757d;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px);
    color: white;
}

.social-link.facebook { background: #3b5998; }
.social-link.twitter { background: #1da1f2; }
.social-link.linkedin { background: #0077b5; }
.social-link.instagram { background: #e4405f; }
.social-link.youtube { background: #ff0000; }

.faq-section {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 40px;
    margin-top: 40px;
}

.faq-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: 100%;
}

.faq-item h6 {
    margin-bottom: 15px;
    font-weight: 600;
}

.faq-item p {
    margin: 0;
    color: #6c757d;
    line-height: 1.6;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.btn {
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.4);
}

@media (max-width: 768px) {
    .contact-form-card,
    .contact-info {
        padding: 25px;
        margin-bottom: 30px;
    }

    .contact-info {
        position: static;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        margin: 0 auto 15px;
    }

    .social-links {
        justify-content: center;
    }

    .faq-section {
        padding: 25px;
    }
}

/* تأثيرات الحركة */
.contact-form-card,
.contact-info,
.faq-item {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تحديث نوع الاستفسار تلقائياً حسب الموضوع
document.getElementById('subject').addEventListener('input', function() {
    const subject = this.value.toLowerCase();
    const typeSelect = document.getElementById('type');

    if (subject.includes('دعم') || subject.includes('مشكلة') || subject.includes('خطأ')) {
        typeSelect.value = 'support';
    } else if (subject.includes('فوترة') || subject.includes('اشتراك') || subject.includes('دفع')) {
        typeSelect.value = 'billing';
    } else if (subject.includes('ميزة') || subject.includes('تطوير') || subject.includes('اقتراح')) {
        typeSelect.value = 'feature';
    } else if (subject.includes('شراكة') || subject.includes('تعاون')) {
        typeSelect.value = 'partnership';
    }
});

// عداد الأحرف للرسالة
const messageTextarea = document.getElementById('message');
const maxLength = 1000;

// إنشاء عداد الأحرف
const charCounter = document.createElement('div');
charCounter.className = 'form-text text-end';
charCounter.style.marginTop = '5px';
messageTextarea.parentNode.appendChild(charCounter);

function updateCharCounter() {
    const currentLength = messageTextarea.value.length;
    const remaining = maxLength - currentLength;

    charCounter.textContent = `${currentLength}/${maxLength} حرف`;

    if (remaining < 50) {
        charCounter.style.color = '#dc3545';
    } else if (remaining < 100) {
        charCounter.style.color = '#ffc107';
    } else {
        charCounter.style.color = '#6c757d';
    }
}

messageTextarea.addEventListener('input', updateCharCounter);
messageTextarea.setAttribute('maxlength', maxLength);
updateCharCounter();

// تأثيرات بصرية للنموذج
const formInputs = document.querySelectorAll('.form-control, .form-select');
formInputs.forEach(input => {
    input.addEventListener('focus', function() {
        this.parentNode.classList.add('focused');
    });

    input.addEventListener('blur', function() {
        this.parentNode.classList.remove('focused');
    });
});

// إضافة تأثير للتركيز
const style = document.createElement('style');
style.textContent = `
    .focused .form-label {
        color: #007bff;
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>
