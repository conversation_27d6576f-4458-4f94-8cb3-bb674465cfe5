-- أوامر SQL لإصلاح مشكلة عمود user_id في جدول العملاء
-- يمكن تنفيذ هذه الأوامر في phpMyAdmin أو أي أداة إدارة قواعد بيانات

-- ===================================
-- 1. فحص وجود جدول العملاء
-- ===================================
SHOW TABLES LIKE 'clients';

-- ===================================
-- 2. فحص هيكل جدول العملاء الحالي
-- ===================================
DESCRIBE clients;

-- ===================================
-- 3. إض<PERSON><PERSON>ة عمود user_id إذا كان مفقود
-- ===================================
-- تحذير: تأكد من أن العمود غير موجود قبل تنفيذ هذا الأمر
ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id;

-- ===================================
-- 4. إضافة فهرس لعمود user_id (للأداء)
-- ===================================
ALTER TABLE clients ADD INDEX idx_user_id (user_id);

-- ===================================
-- 5. ربط العملاء الموجودين بأول مستخدم نشط
-- ===================================
-- البحث عن أول مستخدم نشط
SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1;

-- تحديث جميع العملاء لربطهم بالمستخدم ID = 1 (غير هذا الرقم حسب الحاجة)
UPDATE clients SET user_id = 1 WHERE user_id IS NULL OR user_id = 0;

-- ===================================
-- 6. إنشاء جدول العملاء من الصفر (إذا لم يكن موجود)
-- ===================================
CREATE TABLE IF NOT EXISTS clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL DEFAULT 1,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    company VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    tax_number VARCHAR(50),
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_email (email),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- 7. إضافة عملاء تجريبيين (اختياري)
-- ===================================
-- تأكد من وجود مستخدم بـ ID = 1 أولاً
INSERT INTO clients (user_id, name, email, phone, company, is_active) VALUES
(1, 'أحمد محمد علي', '<EMAIL>', '0501234567', 'شركة التقنية المتقدمة', 1),
(1, 'فاطمة سالم أحمد', '<EMAIL>', '0509876543', 'مؤسسة الإبداع التجاري', 1),
(1, 'محمد عبدالله سعد', '<EMAIL>', '0551234567', 'شركة الحلول الذكية', 1),
(1, 'نورا خالد محمد', '<EMAIL>', '0561234567', 'مجموعة الأعمال المتكاملة', 1);

-- ===================================
-- 8. اختبار الاستعلامات
-- ===================================
-- فحص عدد العملاء لمستخدم معين
SELECT COUNT(*) FROM clients c WHERE c.user_id = 1 AND c.is_active = 1;

-- اختبار الاستعلام المعقد
SELECT c.*, 
       COALESCE(COUNT(i.id), 0) as invoice_count
FROM clients c 
LEFT JOIN invoices i ON c.id = i.client_id
WHERE c.user_id = 1 AND c.is_active = 1
GROUP BY c.id
ORDER BY c.name ASC 
LIMIT 5;

-- ===================================
-- 9. أوامر الطوارئ (في حالة وجود مشاكل)
-- ===================================

-- حفظ نسخة احتياطية من الجدول
CREATE TABLE clients_backup AS SELECT * FROM clients;

-- حذف الجدول القديم (خطر! تأكد من النسخة الاحتياطية)
-- DROP TABLE clients;

-- استعادة البيانات من النسخة الاحتياطية مع إضافة user_id
-- INSERT INTO clients (user_id, name, email, phone, company, address, city, country, tax_number, notes, is_active, created_at)
-- SELECT 1, name, email, phone, company, address, city, country, tax_number, notes, is_active, created_at
-- FROM clients_backup;

-- حذف النسخة الاحتياطية بعد التأكد من نجاح الاستعادة
-- DROP TABLE clients_backup;

-- ===================================
-- 10. التحقق من النجاح
-- ===================================
-- فحص هيكل الجدول النهائي
DESCRIBE clients;

-- فحص عدد السجلات
SELECT COUNT(*) as total_clients FROM clients;

-- فحص توزيع العملاء على المستخدمين
SELECT user_id, COUNT(*) as client_count 
FROM clients 
GROUP BY user_id;

-- ===================================
-- ملاحظات مهمة:
-- ===================================
-- 1. تأكد من عمل نسخة احتياطية قبل تنفيذ أي أمر
-- 2. غير user_id = 1 إلى ID المستخدم الصحيح
-- 3. تأكد من وجود جدول users قبل إضافة المفاتيح الخارجية
-- 4. اختبر الأوامر على بيانات تجريبية أولاً
-- ===================================
