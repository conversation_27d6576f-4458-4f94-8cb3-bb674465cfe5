<?php
// اختبار النظام الكامل بعد الإصلاحات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار النظام الكامل</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".test-links a { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🧪 اختبار النظام الكامل</h1>";

$allGood = true;

// اختبار التكوين
echo "<h2>1. اختبار التكوين</h2>";
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين بنجاح</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    $allGood = false;
}

// اختبار قاعدة البيانات
echo "<h2>2. اختبار قاعدة البيانات</h2>";
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ الاتصال بقاعدة البيانات نجح</div>";
    
    // فحص الجداول المطلوبة
    $requiredTables = ['users', 'clients', 'invoices', 'contact_messages'];
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='result success'>✅ جدول $table موجود</div>";
        } else {
            echo "<div class='result error'>❌ جدول $table غير موجود</div>";
            $allGood = false;
        }
    }
    
    // فحص المستخدم التجريبي
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<div class='result success'>✅ المستخدم التجريبي موجود (admin)</div>";
        echo "<div class='result info'>📧 البريد: " . $admin['email'] . "</div>";
        echo "<div class='result info'>👤 الاسم: " . $admin['first_name'] . " " . $admin['last_name'] . "</div>";
    } else {
        echo "<div class='result error'>❌ المستخدم التجريبي غير موجود</div>";
        $allGood = false;
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
    $allGood = false;
}

// اختبار الملفات الأساسية
echo "<h2>3. اختبار الملفات الأساسية</h2>";
$essentialFiles = [
    'index.php' => 'الصفحة الرئيسية',
    'contact.php' => 'صفحة التواصل',
    'auth/login.php' => 'تسجيل الدخول',
    'auth/register.php' => 'إنشاء حساب',
    'dashboard.php' => 'لوحة التحكم',
    'templates.php' => 'معرض القوالب',
    'pricing.php' => 'خطط الأسعار',
    'help.php' => 'مركز المساعدة'
];

foreach ($essentialFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='result success'>✅ $file ($description)</div>";
    } else {
        echo "<div class='result error'>❌ $file ($description) غير موجود</div>";
        $allGood = false;
    }
}

// اختبار الدوال الأساسية
echo "<h2>4. اختبار الدوال الأساسية</h2>";
$functions = [
    'isLoggedIn' => 'التحقق من تسجيل الدخول',
    'sanitize' => 'تنظيف البيانات',
    'getDBConnection' => 'الاتصال بقاعدة البيانات',
    'setMessage' => 'تعيين الرسائل',
    'getMessage' => 'الحصول على الرسائل',
    'redirect' => 'إعادة التوجيه'
];

foreach ($functions as $func => $description) {
    if (function_exists($func)) {
        echo "<div class='result success'>✅ $func() - $description</div>";
    } else {
        echo "<div class='result error'>❌ $func() - $description غير موجودة</div>";
        $allGood = false;
    }
}

// النتيجة النهائية
echo "<h2>5. النتيجة النهائية</h2>";
if ($allGood) {
    echo "<div class='result success'>";
    echo "<h3>🎉 جميع الاختبارات نجحت!</h3>";
    echo "<p><strong>✅ قاعدة البيانات جاهزة</strong></p>";
    echo "<p><strong>✅ المستخدم التجريبي موجود</strong></p>";
    echo "<p><strong>✅ جميع الملفات موجودة</strong></p>";
    echo "<p><strong>✅ جميع الدوال تعمل</strong></p>";
    echo "<p><strong>✅ النظام جاهز للاستخدام</strong></p>";
    echo "</div>";
} else {
    echo "<div class='result error'>";
    echo "<h3>❌ هناك مشاكل تحتاج للحل</h3>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه وتشغيل setup_database.php إذا لزم الأمر</p>";
    echo "</div>";
}

// روابط الاختبار
echo "<h2>6. اختبار الصفحات</h2>";
echo "<div class='test-links'>";
echo "<a href='setup_database.php' target='_blank'>🔧 إعداد قاعدة البيانات</a>";
echo "<a href='index.php' target='_blank'>🏠 الصفحة الرئيسية</a>";
echo "<a href='contact.php' target='_blank'>📧 صفحة التواصل</a>";
echo "<a href='auth/login.php' target='_blank'>🔐 تسجيل الدخول</a>";
echo "<a href='auth/register.php' target='_blank'>👤 إنشاء حساب</a>";
echo "<a href='templates.php' target='_blank'>🎨 معرض القوالب</a>";
echo "<a href='pricing.php' target='_blank'>💰 خطط الأسعار</a>";
echo "<a href='help.php' target='_blank'>❓ مركز المساعدة</a>";
echo "</div>";

echo "<h2>7. بيانات الدخول التجريبية</h2>";
echo "<div class='result info'>";
echo "<h4>🔑 بيانات المستخدم التجريبي:</h4>";
echo "<p><strong>اسم المستخدم:</strong> <code>admin</code></p>";
echo "<p><strong>كلمة المرور:</strong> <code>123456</code></p>";
echo "<p><strong>البريد الإلكتروني:</strong> <code><EMAIL></code></p>";
echo "</div>";

echo "<h2>8. الخطوات التالية</h2>";
echo "<div class='result info'>";
echo "<h4>📋 ما تم إصلاحه:</h4>";
echo "<ul>";
echo "<li>✅ إضافة حقل اسم المستخدم في جدول المستخدمين</li>";
echo "<li>✅ تحديث صفحة تسجيل الدخول لدعم اسم المستخدم والبريد الإلكتروني</li>";
echo "<li>✅ تحديث صفحة إنشاء الحساب لتشمل اسم المستخدم</li>";
echo "<li>✅ إصلاح روابط المصادقة في الهيدر</li>";
echo "<li>✅ إنشاء قاعدة البيانات والجداول المطلوبة</li>";
echo "<li>✅ إنشاء مستخدم تجريبي للاختبار</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
