{"name": "invoice-saas/invoice-platform", "description": "منصة SaaS متكاملة لإنشاء وإدارة الفواتير مع دعم اللغة العربية", "type": "project", "keywords": ["invoice", "saas", "billing", "php", "mysql", "arabic", "rtl"], "homepage": "https://github.com/your-username/invoice-saas", "license": "MIT", "authors": [{"name": "Your Name", "email": "<EMAIL>", "homepage": "https://yourwebsite.com", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/your-username/invoice-saas/issues", "source": "https://github.com/your-username/invoice-saas"}, "require": {"php": ">=7.4", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-mbstring": "*", "ext-json": "*", "ext-session": "*", "ext-curl": "*", "ext-gd": "*", "ext-zip": "*", "tecnickcom/tcpdf": "^6.4", "phpmailer/phpmailer": "^6.8", "stripe/stripe-php": "^10.0", "paypal/rest-api-sdk-php": "^1.14", "aws/aws-sdk-php": "^3.0", "predis/predis": "^2.0", "monolog/monolog": "^3.0", "vlucas/phpdotenv": "^5.5", "firebase/php-jwt": "^6.8", "ramsey/uuid": "^4.7", "league/csv": "^9.8", "phpoffice/phpspreadsheet": "^1.28", "intervention/image": "^2.7", "carbon/carbon": "^2.68", "respect/validation": "^2.2", "twig/twig": "^3.6", "symfony/console": "^6.3", "symfony/http-foundation": "^6.3", "guzzlehttp/guzzle": "^7.7", "league/flysystem": "^3.15", "league/flysystem-aws-s3-v3": "^3.15"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "phpstan/phpstan": "^1.10", "friendsofphp/php-cs-fixer": "^3.21", "phpmd/phpmd": "^2.13", "sebastian/phpcpd": "^6.0", "phploc/phploc": "^7.0", "mockery/mockery": "^1.6", "fakerphp/faker": "^1.23"}, "autoload": {"psr-4": {"InvoiceSaas\\": "src/", "InvoiceSaas\\Controllers\\": "src/Controllers/", "InvoiceSaas\\Models\\": "src/Models/", "InvoiceSaas\\Services\\": "src/Services/", "InvoiceSaas\\Utils\\": "src/Utils/", "InvoiceSaas\\Middleware\\": "src/Middleware/"}, "files": ["includes/functions.php"]}, "autoload-dev": {"psr-4": {"InvoiceSaas\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "cs-check": "phpcs --standard=PSR12 src/", "cs-fix": "phpcbf --standard=PSR12 src/", "stan": "phpstan analyse src/ --level=8", "php-cs-fixer": "php-cs-fixer fix src/", "md": "phpmd src/ text cleancode,codesize,controversial,design,naming,unusedcode", "cpd": "phpcpd src/", "loc": "phploc src/", "quality": ["@cs-check", "@stan", "@md", "@cpd"], "install-hooks": "cp scripts/pre-commit .git/hooks/pre-commit && chmod +x .git/hooks/pre-commit", "post-install-cmd": ["@install-hooks"], "post-update-cmd": ["@install-hooks"], "serve": "php -S localhost:8000 -t public/", "migrate": "php scripts/migrate.php", "seed": "php scripts/seed.php", "backup": "php scripts/backup.php", "clear-cache": "php scripts/clear-cache.php", "generate-docs": "phpdoc -d src/ -t docs/", "security-check": "composer audit"}, "scripts-descriptions": {"test": "تشغيل جميع الاختبارات", "test-coverage": "تشغيل الاختبارات مع تقرير التغطية", "cs-check": "فحص معايير الكود", "cs-fix": "إصلاح معايير الكود تلقائياً", "stan": "تحليل الكود الثابت", "php-cs-fixer": "إصلاح تنسيق الكود", "md": "كشف الأكواد المعقدة", "cpd": "كشف الأكواد المكررة", "loc": "عد أسطر الكود", "quality": "فحص جودة الكود الشامل", "serve": "تشغيل خادم التطوير", "migrate": "تشغيل ترحيل قاعدة البيانات", "seed": "ملء قاعدة البيانات بالبيانات التجريبية", "backup": "إنشاء نسخة احتياطية", "clear-cache": "مس<PERSON> التخزين المؤقت", "security-check": "فحص الثغرات الأمنية"}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}, "platform": {"php": "7.4"}}, "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "minimum-stability": "stable", "prefer-stable": true, "archive": {"exclude": ["/tests", "/docs", "/scripts", "/.github", "/.giti<PERSON>re", "/phpunit.xml", "/phpcs.xml", "/phpstan.neon", "/.php-cs-fixer.php"]}}