<?php
// أمر مباشر لإضافة عمود user_id
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إضافة عمود user_id</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-danger { background: #dc3545; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-family: monospace; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>⚡ إضافة عمود user_id مباشرة</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// الاتصال بقاعدة البيانات
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

echo "<h2>🔍 فحص الوضع الحالي</h2>";

// فحص وجود الجدول
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'clients'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<div class='result error'>❌ جدول العملاء غير موجود</div>";
        echo "<div class='result info'>يجب إنشاء الجدول أولاً</div>";
        echo "<a href='fix_user_id_column.php?action=create_table' class='btn btn-danger'>📋 إنشاء جدول جديد</a>";
        exit;
    }
    
    echo "<div class='result success'>✅ جدول العملاء موجود</div>";
    
    // فحص الأعمدة الموجودة
    $stmt = $pdo->query("DESCRIBE clients");
    $columns = $stmt->fetchAll();
    
    echo "<div class='result info'>";
    echo "<strong>الأعمدة الموجودة:</strong><br>";
    $hasUserId = false;
    foreach ($columns as $column) {
        echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
        if ($column['Field'] == 'user_id') {
            $hasUserId = true;
        }
    }
    echo "</div>";
    
    if ($hasUserId) {
        echo "<div class='result warning'>⚠️ عمود user_id موجود بالفعل!</div>";
        echo "<div class='result success'>المشكلة محلولة - يمكنك اختبار الصفحة</div>";
        echo "<a href='clients.php' class='btn btn-success' target='_blank'>🔗 اختبار صفحة العملاء</a>";
        exit;
    }
    
    echo "<div class='result error'>❌ عمود user_id مفقود</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</div>";
    exit;
}

echo "<h2>⚡ تنفيذ أمر إضافة العمود</h2>";

try {
    // عرض الأمر الذي سيتم تنفيذه
    echo "<div class='result info'>";
    echo "<strong>الأمر الذي سيتم تنفيذه:</strong><br>";
    echo "<pre>ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id;</pre>";
    echo "</div>";
    
    // تنفيذ الأمر
    echo "<div class='result info'>🔄 جاري تنفيذ الأمر...</div>";
    
    $pdo->exec("ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
    echo "<div class='result success'>✅ تم إضافة عمود user_id بنجاح!</div>";
    
    // إضافة فهرس
    echo "<div class='result info'>🔄 جاري إضافة فهرس...</div>";
    try {
        $pdo->exec("ALTER TABLE clients ADD INDEX idx_user_id (user_id)");
        echo "<div class='result success'>✅ تم إضافة فهرس user_id</div>";
    } catch (Exception $e) {
        echo "<div class='result warning'>⚠️ لم يتم إضافة الفهرس: " . $e->getMessage() . "</div>";
    }
    
    // فحص عدد السجلات الموجودة
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
    $clientCount = $stmt->fetchColumn();
    
    if ($clientCount > 0) {
        echo "<div class='result info'>يوجد $clientCount سجل في الجدول</div>";
        
        // ربط السجلات بأول مستخدم نشط
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
        $firstUser = $stmt->fetch();
        
        if ($firstUser) {
            $userId = $firstUser['id'];
            echo "<div class='result info'>🔄 جاري ربط السجلات بالمستخدم ID: $userId</div>";
            
            $stmt = $pdo->prepare("UPDATE clients SET user_id = ? WHERE user_id = 1");
            $stmt->execute([$userId]);
            
            echo "<div class='result success'>✅ تم ربط جميع السجلات بالمستخدم ID: $userId</div>";
        } else {
            echo "<div class='result warning'>⚠️ لا يوجد مستخدمين نشطين - السجلات مربوطة بـ user_id = 1</div>";
        }
    } else {
        echo "<div class='result info'>لا يوجد سجلات في الجدول</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ فشل في إضافة العمود: " . $e->getMessage() . "</div>";
    echo "<div class='result info'>";
    echo "<strong>الأخطاء الشائعة وحلولها:</strong><br>";
    echo "- إذا كان الخطأ 'Duplicate column name': العمود موجود بالفعل<br>";
    echo "- إذا كان الخطأ 'Table doesn't exist': الجدول غير موجود<br>";
    echo "- إذا كان خطأ في الصلاحيات: تحقق من صلاحيات قاعدة البيانات<br>";
    echo "</div>";
    
    echo "<h3>حلول بديلة:</h3>";
    echo "<a href='fix_user_id_column.php?action=create_table' class='btn btn-danger'>📋 إنشاء جدول جديد</a>";
    echo "<a href='fix_user_id_column.php' class='btn'>🛠️ إصلاح شامل</a>";
    exit;
}

echo "<h2>🧪 اختبار النتيجة</h2>";

try {
    // فحص أن العمود تم إضافته
    $stmt = $pdo->query("DESCRIBE clients");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('user_id', $columns)) {
        echo "<div class='result success'>✅ عمود user_id موجود الآن!</div>";
        
        // اختبار الاستعلام المُشكِل
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['user_id'])) {
            $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
            $user = $stmt->fetch();
            if ($user) {
                $_SESSION['user_id'] = $user['id'];
            }
        }
        
        if (isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
            
            // اختبار الاستعلام الذي كان يفشل
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients c WHERE c.user_id = ? AND c.is_active = 1");
            $stmt->execute([$userId]);
            $count = $stmt->fetchColumn();
            
            echo "<div class='result success'>✅ الاستعلام المُشكِل يعمل الآن!</div>";
            echo "<div class='result info'>عدد العملاء: $count</div>";
            
            if ($count == 0) {
                echo "<div class='result warning'>⚠️ لا يوجد عملاء - قد تحتاج لإضافة بيانات تجريبية</div>";
                echo "<a href='fix_user_id_column.php' class='btn'>🔧 إضافة بيانات تجريبية</a>";
            }
        }
        
    } else {
        echo "<div class='result error'>❌ العمود لم يتم إضافته بنجاح</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار النتيجة: " . $e->getMessage() . "</div>";
}

echo "<h2>🎉 النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>تم إضافة عمود user_id بنجاح!</h3>";
echo "<p><strong>ما تم عمله:</strong></p>";
echo "<ul>";
echo "<li>✅ إضافة عمود user_id إلى جدول العملاء</li>";
echo "<li>✅ إضافة فهرس للأداء</li>";
echo "<li>✅ ربط السجلات الموجودة بالمستخدمين</li>";
echo "<li>✅ اختبار الاستعلامات</li>";
echo "</ul>";
echo "</div>";

echo "<h3>اختبار الصفحات:</h3>";
echo "<a href='clients.php' class='btn btn-success' target='_blank'>🔗 صفحة العملاء</a>";
echo "<a href='dashboard.php' class='btn' target='_blank'>🔗 لوحة التحكم</a>";
echo "<a href='create-invoice.php' class='btn' target='_blank'>🔗 إنشاء فاتورة</a>";

echo "<div class='result info'>";
echo "<strong>ملاحظة:</strong> إذا كانت المشكلة محلولة، يمكنك حذف ملفات الإصلاح هذه.";
echo "</div>";

echo "</body></html>";
?>
