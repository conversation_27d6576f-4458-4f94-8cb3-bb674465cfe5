<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

// معالجة إضافة/تعديل العملاء
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getDBConnection();

        if (isset($_POST['action'])) {
            if ($_POST['action'] == 'add_client') {
                // إضافة عميل جديد
                $name = sanitize($_POST['name']);
                $email = sanitize($_POST['email']);
                $phone = sanitize($_POST['phone']);
                $company = sanitize($_POST['company']);
                $address = sanitize($_POST['address']);
                $city = sanitize($_POST['city']);
                $country = sanitize($_POST['country']);
                $tax_number = sanitize($_POST['tax_number']);
                $notes = sanitize($_POST['notes']);

                if (empty($name)) {
                    throw new Exception('اسم العميل مطلوب');
                }

                $stmt = $pdo->prepare("
                    INSERT INTO clients (user_id, name, email, phone, company, address, city, country, tax_number, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([$user_id, $name, $email, $phone, $company, $address, $city, $country, $tax_number, $notes]);
                $success = 'تم إضافة العميل بنجاح';

            } elseif ($_POST['action'] == 'edit_client') {
                // تعديل عميل موجود
                $client_id = (int)$_POST['client_id'];
                $name = sanitize($_POST['name']);
                $email = sanitize($_POST['email']);
                $phone = sanitize($_POST['phone']);
                $company = sanitize($_POST['company']);
                $address = sanitize($_POST['address']);
                $city = sanitize($_POST['city']);
                $country = sanitize($_POST['country']);
                $tax_number = sanitize($_POST['tax_number']);
                $notes = sanitize($_POST['notes']);

                if (empty($name)) {
                    throw new Exception('اسم العميل مطلوب');
                }

                $stmt = $pdo->prepare("
                    UPDATE clients
                    SET name = ?, email = ?, phone = ?, company = ?, address = ?, city = ?, country = ?, tax_number = ?, notes = ?
                    WHERE id = ? AND user_id = ?
                ");

                $stmt->execute([$name, $email, $phone, $company, $address, $city, $country, $tax_number, $notes, $client_id, $user_id]);
                $success = 'تم تحديث بيانات العميل بنجاح';

            } elseif ($_POST['action'] == 'delete_client') {
                // حذف عميل (تعطيل)
                $client_id = (int)$_POST['client_id'];

                $stmt = $pdo->prepare("UPDATE clients SET is_active = 0 WHERE id = ? AND user_id = ?");
                $stmt->execute([$client_id, $user_id]);
                $success = 'تم حذف العميل بنجاح';
            }
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $pdo = getDBConnection();

    // التأكد من وجود الحقول المطلوبة في جدول العملاء
    try {
        $pdo->exec("ALTER TABLE clients ADD COLUMN tax_number VARCHAR(50) DEFAULT NULL");
    } catch (Exception $e) {
        // الحقل موجود بالفعل
    }

    try {
        $pdo->exec("ALTER TABLE clients ADD COLUMN notes TEXT DEFAULT NULL");
    } catch (Exception $e) {
        // الحقل موجود بالفعل
    }
    
    // بناء استعلام البحث
    $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
    $params = [$user_id];

    if (!empty($search)) {
        $where_conditions[] = "(c.name LIKE ? OR c.email LIKE ? OR c.company LIKE ? OR c.phone LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $where_clause = implode(' AND ', $where_conditions);
    
    // عدد العملاء الإجمالي
    $countSql = "SELECT COUNT(*) FROM clients WHERE $where_clause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total_clients = $countStmt->fetchColumn();
    $total_pages = ceil($total_clients / $per_page);
    
    // جلب العملاء - استعلام محسن
    $sql = "
        SELECT c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country,
               c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at,
               COALESCE(COUNT(i.id), 0) as invoice_count,
               COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END), 0) as total_paid,
               COALESCE(SUM(CASE WHEN i.status != 'paid' AND i.status IS NOT NULL THEN i.total_amount ELSE 0 END), 0) as total_pending,
               MAX(i.created_at) as last_invoice_date
        FROM clients c
        LEFT JOIN invoices i ON c.id = i.client_id
        WHERE $where_clause
        GROUP BY c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at
        ORDER BY c.name ASC
        LIMIT $per_page OFFSET $offset
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $clients = $stmt->fetchAll();

    // Debug information and fallback (remove in production)
    if (empty($clients)) {
        // Try a simple query to see if there are any clients at all
        $debugStmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ? AND is_active = 1");
        $debugStmt->execute([$user_id]);
        $debugCount = $debugStmt->fetchColumn();

        if ($debugCount > 0) {
            error_log("Debug: Found $debugCount clients for user $user_id but complex query returned empty");

            // Fallback to simple query without JOINs
            $simpleSql = "SELECT c.*, 0 as invoice_count, 0 as total_paid, 0 as total_pending, NULL as last_invoice_date
                         FROM clients c
                         WHERE $where_clause
                         ORDER BY c.name ASC
                         LIMIT $per_page OFFSET $offset";

            $simpleStmt = $pdo->prepare($simpleSql);
            $simpleStmt->execute($params);
            $clients = $simpleStmt->fetchAll();

            if (!empty($clients)) {
                error_log("Debug: Fallback simple query returned " . count($clients) . " clients");
            }
        }
    }
    
} catch (Exception $e) {
    $clients = [];
    $total_clients = 0;
    $total_pages = 0;
    $error = "خطأ في جلب بيانات العملاء: " . $e->getMessage();
    error_log("Clients retrieval error: " . $e->getMessage());
}

$page_title = 'إدارة العملاء';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h2><i class="fas fa-users me-2"></i>إدارة العملاء</h2>
            <p class="text-muted">إدارة قاعدة بيانات عملائك</p>
        </div>
        <div class="col-md-6 text-end">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                <i class="fas fa-plus me-1"></i>إضافة عميل جديد
            </button>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" name="search" 
                           placeholder="اسم العميل، البريد الإلكتروني، الشركة..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="col-md-6">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                        <a href="clients.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>إعادة تعيين
                        </a>
                        <button type="button" class="btn btn-outline-success" onclick="exportClients()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Clients Grid -->
    <div class="row">
        <?php if (empty($clients)): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5>لا يوجد عملاء</h5>
                    <p class="text-muted">لم يتم العثور على عملاء يطابقون معايير البحث</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                        <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                    </button>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($clients as $client): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="client-card">
                        <div class="client-header">
                            <div class="client-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="client-info">
                                <h5 class="client-name"><?php echo htmlspecialchars($client['name']); ?></h5>
                                <?php if ($client['company']): ?>
                                    <p class="client-company"><?php echo htmlspecialchars($client['company']); ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="client-actions">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="editClient(<?php echo $client['id']; ?>)">
                                            <i class="fas fa-edit me-2"></i>تحرير
                                        </a></li>
                                        <li><a class="dropdown-item" href="create-invoice.php?client=<?php echo $client['id']; ?>">
                                            <i class="fas fa-file-invoice me-2"></i>إنشاء فاتورة
                                        </a></li>
                                        <li><a class="dropdown-item" href="invoices.php?client=<?php echo $client['id']; ?>">
                                            <i class="fas fa-list me-2"></i>عرض الفواتير
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteClient(<?php echo $client['id']; ?>)">
                                            <i class="fas fa-trash me-2"></i>حذف
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="client-details">
                            <?php if ($client['email']): ?>
                                <div class="detail-item">
                                    <i class="fas fa-envelope text-muted me-2"></i>
                                    <span><?php echo htmlspecialchars($client['email']); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($client['phone']): ?>
                                <div class="detail-item">
                                    <i class="fas fa-phone text-muted me-2"></i>
                                    <span><?php echo htmlspecialchars($client['phone']); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($client['address']): ?>
                                <div class="detail-item">
                                    <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                    <span><?php echo htmlspecialchars($client['address']); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="client-stats">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo number_format($client['invoice_count']); ?></div>
                                <div class="stat-label">فاتورة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?php echo formatCurrency($client['total_paid']); ?></div>
                                <div class="stat-label">مدفوع</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?php echo formatCurrency($client['total_pending']); ?></div>
                                <div class="stat-label">معلق</div>
                            </div>
                        </div>
                        
                        <?php if ($client['last_invoice_date']): ?>
                            <div class="client-footer">
                                <small class="text-muted">
                                    آخر فاتورة: <?php echo formatDate($client['last_invoice_date'], 'd/m/Y'); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="row">
            <div class="col-12">
                <nav aria-label="صفحات العملاء">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Add/Edit Client Modal -->
<div class="modal fade" id="addClientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clientModalTitle">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="client-form" method="POST">
                    <input type="hidden" id="client_id" name="client_id">
                    <input type="hidden" name="action" value="add_client">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="client_name" class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" id="client_name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="client_company" class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" id="client_company" name="company">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="client_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="client_email" name="email">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="client_phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="client_phone" name="phone">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="client_city" class="form-label">المدينة</label>
                            <input type="text" class="form-control" id="client_city" name="city">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="client_country" class="form-label">الدولة</label>
                            <input type="text" class="form-control" id="client_country" name="country">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="client_address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="client_address" name="address" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="client_tax_number" class="form-label">الرقم الضريبي</label>
                            <input type="text" class="form-control" id="client_tax_number" name="tax_number">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="client_notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="client_notes" name="notes" rows="2"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="client-form" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>حفظ العميل
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.client-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.client-card:hover {
    transform: translateY(-5px);
}

.client-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.client-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-left: 15px;
}

.client-info {
    flex: 1;
}

.client-name {
    margin: 0 0 5px;
    font-size: 1.1rem;
    font-weight: 600;
}

.client-company {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.client-actions {
    margin-right: 10px;
}

.client-details {
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.client-stats {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-number {
    font-size: 1.1rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.client-footer {
    text-align: center;
}

@media (max-width: 768px) {
    .client-header {
        flex-direction: column;
        text-align: center;
    }

    .client-avatar {
        margin: 0 auto 15px;
    }

    .client-actions {
        margin: 15px 0 0;
    }

    .client-stats {
        flex-direction: column;
        gap: 10px;
    }
}
</style>

<script>
let currentClientId = null;

function editClient(clientId) {
    currentClientId = clientId;

    // جلب بيانات العميل
    fetch('ajax/get-client.php?id=' + clientId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const client = data.client;

                // ملء النموذج
                document.getElementById('client_id').value = client.id;
                document.getElementById('client_name').value = client.name || '';
                document.getElementById('client_company').value = client.company || '';
                document.getElementById('client_email').value = client.email || '';
                document.getElementById('client_phone').value = client.phone || '';
                document.getElementById('client_city').value = client.city || '';
                document.getElementById('client_country').value = client.country || '';
                document.getElementById('client_address').value = client.address || '';
                document.getElementById('client_tax_number').value = client.tax_number || '';
                document.getElementById('client_notes').value = client.notes || '';

                // تغيير عنوان المودال
                document.getElementById('clientModalTitle').textContent = 'تحرير العميل';

                // إظهار المودال
                new bootstrap.Modal(document.getElementById('addClientModal')).show();
            } else {
                alert('حدث خطأ في جلب بيانات العميل');
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال بالخادم');
        });
}

function saveClientData() {
    const formData = new FormData(document.getElementById('client-form'));

    if (!formData.get('name').trim()) {
        alert('يرجى إدخال اسم العميل');
        return;
    }

    fetch('ajax/save-client-data.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ العميل بنجاح');
            location.reload();
        } else {
            alert('حدث خطأ في حفظ العميل: ' + data.message);
        }
    })
    .catch(error => {
        alert('حدث خطأ في الاتصال بالخادم');
    });
}

function deleteClient(clientId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟ سيتم حذف جميع الفواتير المرتبطة به أيضاً.')) {
        fetch('ajax/delete-client.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ client_id: clientId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف العميل بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ في حذف العميل: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    }
}

function exportClients() {
    window.location.href = 'export-clients.php';
}

// إعادة تعيين النموذج عند إغلاق المودال
document.getElementById('addClientModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('client-form').reset();
    document.getElementById('client_id').value = '';
    document.getElementById('clientModalTitle').textContent = 'إضافة عميل جديد';
    currentClientId = null;
});
</script>

<?php include 'includes/footer.php'; ?>
