<?php
// فحص شامل للنظام بعد الإصلاح
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>فحص النظام الشامل</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }";
echo ".container { max-width: 1000px; margin: 20px auto; }";
echo ".check-item { margin: 10px 0; padding: 10px; border-radius: 5px; }";
echo ".check-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }";
echo ".check-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }";
echo ".check-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>فحص النظام الشامل</h1>";

$allGood = true;

// 1. فحص PHP
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>1. فحص PHP</h3></div>";
echo "<div class='card-body'>";

echo "<div class='check-item check-success'>";
echo "✅ إصدار PHP: " . phpversion();
echo "</div>";

$requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'session'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='check-item check-success'>✅ امتداد $ext متوفر</div>";
    } else {
        echo "<div class='check-item check-error'>❌ امتداد $ext غير متوفر</div>";
        $allGood = false;
    }
}

echo "</div></div>";

// 2. فحص الملفات
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>2. فحص الملفات الأساسية</h3></div>";
echo "<div class='card-body'>";

$requiredFiles = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php',
    '.htaccess'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "<div class='check-item check-success'>✅ $file موجود</div>";
    } else {
        echo "<div class='check-item check-error'>❌ $file غير موجود</div>";
        $allGood = false;
    }
}

echo "</div></div>";

// 3. فحص المجلدات
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>3. فحص المجلدات</h3></div>";
echo "<div class='card-body'>";

$requiredDirs = [
    'assets',
    'assets/css',
    'assets/js',
    'assets/images',
    'assets/uploads',
    'assets/temp',
    'logs',
    'config',
    'includes'
];

foreach ($requiredDirs as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        $class = is_writable($dir) ? 'check-success' : 'check-warning';
        echo "<div class='check-item $class'>✅ $dir موجود ($writable)</div>";
    } else {
        echo "<div class='check-item check-error'>❌ $dir غير موجود</div>";
        $allGood = false;
    }
}

echo "</div></div>";

// 4. فحص قاعدة البيانات
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>4. فحص قاعدة البيانات</h3></div>";
echo "<div class='card-body'>";

try {
    // محاولة الاتصال
    $pdo = new PDO('mysql:host=localhost;charset=utf8mb4', 'root', '');
    echo "<div class='check-item check-success'>✅ الاتصال بـ MySQL نجح</div>";
    
    // التحقق من قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE 'invoice_saas'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='check-item check-success'>✅ قاعدة البيانات invoice_saas موجودة</div>";
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO('mysql:host=localhost;dbname=invoice_saas;charset=utf8mb4', 'root', '');
        
        // فحص الجداول
        $tables = ['users', 'contact_messages'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='check-item check-success'>✅ جدول $table موجود</div>";
            } else {
                echo "<div class='check-item check-warning'>⚠️ جدول $table غير موجود (سيتم إنشاؤه تلقائياً)</div>";
            }
        }
        
        // فحص المستخدم التجريبي
        $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
        if ($stmt && $stmt->fetchColumn() > 0) {
            echo "<div class='check-item check-success'>✅ المستخدم التجريبي موجود (admin / 123456)</div>";
        } else {
            echo "<div class='check-item check-warning'>⚠️ المستخدم التجريبي غير موجود (سيتم إنشاؤه تلقائياً)</div>";
        }
        
    } else {
        echo "<div class='check-item check-warning'>⚠️ قاعدة البيانات invoice_saas غير موجودة (سيتم إنشاؤها تلقائياً)</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='check-item check-error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
    $allGood = false;
}

echo "</div></div>";

// 5. اختبار التكوين
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>5. اختبار التكوين</h3></div>";
echo "<div class='card-body'>";

try {
    require_once 'config/config.php';
    echo "<div class='check-item check-success'>✅ تم تحميل ملف التكوين بنجاح</div>";
    
    if (function_exists('getDBConnection')) {
        echo "<div class='check-item check-success'>✅ دالة getDBConnection متوفرة</div>";
        
        try {
            $db = getDBConnection();
            echo "<div class='check-item check-success'>✅ الاتصال بقاعدة البيانات عبر التكوين نجح</div>";
        } catch (Exception $e) {
            echo "<div class='check-item check-error'>❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='check-item check-error'>❌ دالة getDBConnection غير متوفرة</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='check-item check-error'>❌ خطأ في تحميل التكوين: " . $e->getMessage() . "</div>";
    $allGood = false;
}

echo "</div></div>";

// 6. النتيجة النهائية
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>6. النتيجة النهائية</h3></div>";
echo "<div class='card-body text-center'>";

if ($allGood) {
    echo "<div class='alert alert-success'>";
    echo "<h4>🎉 النظام جاهز للاستخدام!</h4>";
    echo "<p>جميع الفحوصات نجحت. يمكنك الآن استخدام النظام.</p>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h4>⚠️ النظام يحتاج لبعض الإصلاحات</h4>";
    echo "<p>هناك بعض المشاكل التي تحتاج للحل. راجع التفاصيل أعلاه.</p>";
    echo "</div>";
}

echo "</div></div>";

// 7. روابط الاختبار
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>7. اختبار الصفحات</h3></div>";
echo "<div class='card-body'>";

$testPages = [
    'index.php' => 'الصفحة الرئيسية',
    'contact.php' => 'صفحة التواصل',
    'contact_simple.php' => 'صفحة التواصل المبسطة',
    'templates.php' => 'معرض القوالب',
    'pricing.php' => 'خطط الأسعار',
    'help.php' => 'مركز المساعدة',
    'auth/login.php' => 'تسجيل الدخول',
    'auth/register.php' => 'إنشاء حساب'
];

echo "<div class='row'>";
foreach ($testPages as $page => $title) {
    echo "<div class='col-md-6 mb-2'>";
    if (file_exists($page)) {
        echo "<a href='$page' class='btn btn-outline-primary btn-sm w-100' target='_blank'>$title</a>";
    } else {
        echo "<button class='btn btn-outline-secondary btn-sm w-100' disabled>$title (غير موجود)</button>";
    }
    echo "</div>";
}
echo "</div>";

echo "</div></div>";

// 8. معلومات إضافية
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h3>8. معلومات إضافية</h3></div>";
echo "<div class='card-body'>";

echo "<p><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>خادم الويب:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف') . "</p>";
echo "<p><strong>المجلد الحالي:</strong> " . getcwd() . "</p>";
echo "<p><strong>عنوان IP:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'غير معروف') . "</p>";

echo "</div></div>";

echo "</div>"; // إغلاق container

echo "</body></html>";
?>
