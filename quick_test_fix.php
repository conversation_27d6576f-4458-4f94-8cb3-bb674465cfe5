<?php
// اختبار سريع لحل مشكلة user_id
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار سريع - إصلاح user_id</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo ".btn-danger { background: #dc3545; }";
echo ".btn-success { background: #28a745; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>⚡ اختبار سريع - إصلاح مشكلة user_id</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// الاتصال بقاعدة البيانات
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// فحص سريع لجدول العملاء
echo "<h2>فحص جدول العملاء</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'clients'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<div class='result error'>❌ جدول العملاء غير موجود</div>";
        echo "<a href='fix_user_id_column.php' class='btn btn-danger'>🔧 إصلاح فوري</a>";
        exit;
    }
    
    echo "<div class='result success'>✅ جدول العملاء موجود</div>";
    
    // فحص عمود user_id
    $stmt = $pdo->query("DESCRIBE clients");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('user_id', $columns)) {
        echo "<div class='result error'>❌ عمود user_id مفقود</div>";
        echo "<div class='result info'>الأعمدة الموجودة: " . implode(', ', $columns) . "</div>";
        echo "<a href='fix_user_id_column.php' class='btn btn-danger'>🔧 إصلاح فوري</a>";
        exit;
    }
    
    echo "<div class='result success'>✅ عمود user_id موجود</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</div>";
    echo "<a href='fix_user_id_column.php' class='btn btn-danger'>🔧 إصلاح فوري</a>";
    exit;
}

// اختبار الاستعلام المُشكِل
echo "<h2>اختبار الاستعلام المُشكِل</h2>";
try {
    // محاكاة تسجيل الدخول
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id'])) {
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
        $user = $stmt->fetch();
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
        }
    }
    
    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
        
        // هذا هو الاستعلام الذي كان يفشل
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients c WHERE c.user_id = ? AND c.is_active = 1");
        $stmt->execute([$userId]);
        $count = $stmt->fetchColumn();
        
        echo "<div class='result success'>✅ الاستعلام المُشكِل نجح!</div>";
        echo "<div class='result info'>عدد العملاء: $count</div>";
        
        if ($count == 0) {
            echo "<div class='result warning'>⚠️ لا يوجد عملاء - قد تحتاج لإضافة بيانات تجريبية</div>";
            echo "<a href='fix_user_id_column.php' class='btn'>🔧 إضافة بيانات تجريبية</a>";
        }
        
        // اختبار الاستعلام المعقد من clients.php
        $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
        $params = [$userId];
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "
            SELECT c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, 
                   c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at,
                   COALESCE(COUNT(i.id), 0) as invoice_count,
                   COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END), 0) as total_paid,
                   COALESCE(SUM(CASE WHEN i.status != 'paid' AND i.status IS NOT NULL THEN i.total_amount ELSE 0 END), 0) as total_pending,
                   MAX(i.created_at) as last_invoice_date
            FROM clients c 
            LEFT JOIN invoices i ON c.id = i.client_id
            WHERE $where_clause 
            GROUP BY c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at
            ORDER BY c.name ASC 
            LIMIT 20 OFFSET 0
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $clients = $stmt->fetchAll();
        
        echo "<div class='result success'>✅ الاستعلام المعقد من clients.php نجح!</div>";
        echo "<div class='result info'>عدد العملاء المسترجعة: " . count($clients) . "</div>";
        
        if (!empty($clients)) {
            echo "<div class='result info'>";
            echo "<strong>عينة من العملاء:</strong><br>";
            foreach (array_slice($clients, 0, 3) as $client) {
                echo "- " . htmlspecialchars($client['name']) . " (user_id: " . $client['user_id'] . ")<br>";
            }
            echo "</div>";
        }
        
    } else {
        echo "<div class='result warning'>⚠️ لا يمكن اختبار الاستعلامات - لا يوجد مستخدم</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ الاستعلام لا يزال يفشل: " . $e->getMessage() . "</div>";
    echo "<a href='fix_user_id_column.php' class='btn btn-danger'>🔧 إصلاح فوري</a>";
    exit;
}

// اختبار صفحة العملاء
echo "<h2>اختبار صفحة العملاء</h2>";
try {
    // محاولة تحميل جزء من كود clients.php
    ob_start();
    $error_occurred = false;
    
    try {
        // نفس الكود من clients.php
        $search = '';
        $page = 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
        
        $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
        $params = [$userId];
        
        if (!empty($search)) {
            $where_conditions[] = "(c.name LIKE ? OR c.email LIKE ? OR c.company LIKE ? OR c.phone LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $countSql = "SELECT COUNT(*) FROM clients c WHERE $where_clause";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total_clients = $countStmt->fetchColumn();
        
        echo "<div class='result success'>✅ كود صفحة العملاء يعمل بشكل مثالي!</div>";
        echo "<div class='result info'>إجمالي العملاء: $total_clients</div>";
        
    } catch (Exception $e) {
        $error_occurred = true;
        echo "<div class='result error'>❌ كود صفحة العملاء لا يزال يفشل: " . $e->getMessage() . "</div>";
    }
    
    ob_end_clean();
    
    if (!$error_occurred) {
        echo "<div class='result success'>✅ صفحة العملاء جاهزة للعمل!</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار صفحة العملاء: " . $e->getMessage() . "</div>";
}

echo "<h2>النتيجة النهائية</h2>";

if (!$error_occurred) {
    echo "<div class='result success'>";
    echo "<h3>🎉 المشكلة تم حلها بالكامل!</h3>";
    echo "<p>جميع الاستعلامات تعمل بشكل صحيح الآن</p>";
    echo "</div>";
    
    echo "<h3>اختبار الصفحات:</h3>";
    echo "<a href='clients.php' class='btn btn-success' target='_blank'>🔗 صفحة العملاء</a>";
    echo "<a href='dashboard.php' class='btn' target='_blank'>🔗 لوحة التحكم</a>";
    echo "<a href='create-invoice.php' class='btn' target='_blank'>🔗 إنشاء فاتورة</a>";
    
} else {
    echo "<div class='result error'>";
    echo "<h3>❌ المشكلة لا تزال موجودة</h3>";
    echo "<p>يرجى تشغيل الإصلاح الفوري</p>";
    echo "</div>";
    
    echo "<h3>الإصلاح المطلوب:</h3>";
    echo "<a href='fix_user_id_column.php' class='btn btn-danger'>🔧 إصلاح فوري</a>";
}

echo "<div class='result info'>";
echo "<strong>بيانات الدخول:</strong><br>";
echo "اسم المستخدم: admin<br>";
echo "كلمة المرور: 123456";
echo "</div>";

echo "</body></html>";
?>
