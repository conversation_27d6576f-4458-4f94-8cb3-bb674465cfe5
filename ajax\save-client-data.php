<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

$user = getCurrentUser();
$user_id = $user['id'];

// استلام البيانات
$client_id = isset($_POST['client_id']) && !empty($_POST['client_id']) ? (int)$_POST['client_id'] : null;
$name = sanitize($_POST['name'] ?? '');
$company = sanitize($_POST['company'] ?? '');
$email = sanitize($_POST['email'] ?? '');
$phone = sanitize($_POST['phone'] ?? '');
$city = sanitize($_POST['city'] ?? '');
$country = sanitize($_POST['country'] ?? '');
$address = sanitize($_POST['address'] ?? '');
$tax_number = sanitize($_POST['tax_number'] ?? '');
$notes = sanitize($_POST['notes'] ?? '');

// التحقق من البيانات
if (empty($name)) {
    echo json_encode(['success' => false, 'message' => 'اسم العميل مطلوب']);
    exit;
}

if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'البريد الإلكتروني غير صحيح']);
    exit;
}

try {
    $pdo = getDBConnection();
    
    if ($client_id) {
        // تحديث عميل موجود
        
        // التحقق من ملكية العميل
        $checkStmt = $pdo->prepare("SELECT id FROM clients WHERE id = ? AND user_id = ?");
        $checkStmt->execute([$client_id, $user_id]);
        
        if (!$checkStmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
            exit;
        }
        
        // التحقق من عدم تكرار الاسم
        $duplicateStmt = $pdo->prepare("SELECT id FROM clients WHERE user_id = ? AND name = ? AND id != ? AND is_active = 1");
        $duplicateStmt->execute([$user_id, $name, $client_id]);
        
        if ($duplicateStmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'يوجد عميل آخر بنفس الاسم']);
            exit;
        }
        
        // تحديث البيانات
        $stmt = $pdo->prepare("
            UPDATE clients 
            SET name = ?, company = ?, email = ?, phone = ?, city = ?, country = ?, 
                address = ?, tax_number = ?, notes = ?, updated_at = NOW()
            WHERE id = ? AND user_id = ?
        ");
        
        $success = $stmt->execute([
            $name, $company, $email, $phone, $city, $country, 
            $address, $tax_number, $notes, $client_id, $user_id
        ]);
        
        if ($success) {
            // تسجيل النشاط
            $logStmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, description) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $logStmt->execute([
                $user_id,
                'update',
                'client',
                $client_id,
                'تحديث بيانات العميل: ' . $name
            ]);
            
            echo json_encode(['success' => true, 'message' => 'تم تحديث العميل بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ في تحديث العميل']);
        }
        
    } else {
        // إضافة عميل جديد
        
        // التحقق من عدم تكرار الاسم
        $duplicateStmt = $pdo->prepare("SELECT id FROM clients WHERE user_id = ? AND name = ? AND is_active = 1");
        $duplicateStmt->execute([$user_id, $name]);
        
        if ($duplicateStmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'يوجد عميل بنفس الاسم بالفعل']);
            exit;
        }
        
        // إدراج العميل الجديد
        $stmt = $pdo->prepare("
            INSERT INTO clients (user_id, name, company, email, phone, city, country, address, tax_number, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $success = $stmt->execute([
            $user_id, $name, $company, $email, $phone, $city, $country, $address, $tax_number, $notes
        ]);
        
        if ($success) {
            $new_client_id = $pdo->lastInsertId();
            
            // تسجيل النشاط
            $logStmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, description) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $logStmt->execute([
                $user_id,
                'create',
                'client',
                $new_client_id,
                'إضافة عميل جديد: ' . $name
            ]);
            
            echo json_encode([
                'success' => true, 
                'message' => 'تم إضافة العميل بنجاح',
                'client_id' => $new_client_id
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ في إضافة العميل']);
        }
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
