<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// قراءة البيانات من JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['client_id']) || !is_numeric($input['client_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف العميل غير صحيح']);
    exit;
}

$user = getCurrentUser();
$user_id = $user['id'];
$client_id = (int)$input['client_id'];

try {
    $pdo = getDBConnection();
    
    // التحقق من ملكية العميل
    $checkStmt = $pdo->prepare("SELECT name FROM clients WHERE id = ? AND user_id = ? AND is_active = 1");
    $checkStmt->execute([$client_id, $user_id]);
    $client = $checkStmt->fetch();
    
    if (!$client) {
        echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
        exit;
    }
    
    // التحقق من وجود فواتير مرتبطة
    $invoiceStmt = $pdo->prepare("SELECT COUNT(*) FROM invoices WHERE client_id = ?");
    $invoiceStmt->execute([$client_id]);
    $invoice_count = $invoiceStmt->fetchColumn();
    
    // بدء المعاملة
    $pdo->beginTransaction();
    
    try {
        if ($invoice_count > 0) {
            // حذف الفواتير المرتبطة أولاً
            
            // حذف عناصر الفواتير
            $deleteItemsStmt = $pdo->prepare("
                DELETE ii FROM invoice_items ii 
                INNER JOIN invoices i ON ii.invoice_id = i.id 
                WHERE i.client_id = ?
            ");
            $deleteItemsStmt->execute([$client_id]);
            
            // حذف المدفوعات
            $deletePaymentsStmt = $pdo->prepare("
                DELETE p FROM payments p 
                INNER JOIN invoices i ON p.invoice_id = i.id 
                WHERE i.client_id = ?
            ");
            $deletePaymentsStmt->execute([$client_id]);
            
            // حذف الفواتير
            $deleteInvoicesStmt = $pdo->prepare("DELETE FROM invoices WHERE client_id = ?");
            $deleteInvoicesStmt->execute([$client_id]);
        }
        
        // حذف العميل (soft delete)
        $deleteClientStmt = $pdo->prepare("UPDATE clients SET is_active = 0, updated_at = NOW() WHERE id = ? AND user_id = ?");
        $deleteClientStmt->execute([$client_id, $user_id]);
        
        // تسجيل النشاط
        $logStmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, entity_type, entity_id, description) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $logStmt->execute([
            $user_id,
            'delete',
            'client',
            $client_id,
            'حذف العميل: ' . $client['name'] . ($invoice_count > 0 ? " (مع $invoice_count فاتورة)" : '')
        ]);
        
        // تأكيد المعاملة
        $pdo->commit();
        
        echo json_encode([
            'success' => true, 
            'message' => 'تم حذف العميل بنجاح' . ($invoice_count > 0 ? " مع $invoice_count فاتورة مرتبطة" : '')
        ]);
        
    } catch (Exception $e) {
        // التراجع عن المعاملة
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في حذف العميل']);
}
?>
