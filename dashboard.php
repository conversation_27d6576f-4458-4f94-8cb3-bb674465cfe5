<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
if (!$user) {
    setMessage('حدث خطأ في جلب بيانات المستخدم', 'error');
    redirect(APP_URL . '/auth/login.php');
}
$user_id = $user['id'];

try {
    $pdo = getDBConnection();
    if (!$pdo) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // إحصائيات الفواتير
    $invoiceStats = $pdo->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
            COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_invoices,
            COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_invoices,
            COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_invoices,
            SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_revenue,
            SUM(CASE WHEN status != 'paid' THEN total_amount ELSE 0 END) as pending_amount
        FROM invoices 
        WHERE user_id = ?
    ");
    $invoiceStats->execute([$user_id]);
    $stats = $invoiceStats->fetch();
    
    // إحصائيات العملاء
    $clientStats = $pdo->prepare("SELECT COUNT(*) as total_clients FROM clients WHERE user_id = ? AND is_active = 1");
    $clientStats->execute([$user_id]);
    $clientCount = $clientStats->fetch()['total_clients'];
    
    // الفواتير الأخيرة
    $recentInvoices = $pdo->prepare("
        SELECT i.*, c.name as client_name 
        FROM invoices i 
        LEFT JOIN clients c ON i.client_id = c.id 
        WHERE i.user_id = ? 
        ORDER BY i.created_at DESC 
        LIMIT 5
    ");
    $recentInvoices->execute([$user_id]);
    $recent = $recentInvoices->fetchAll();
    
    // الفواتير المتأخرة
    $overdueInvoices = $pdo->prepare("
        SELECT i.*, c.name as client_name 
        FROM invoices i 
        LEFT JOIN clients c ON i.client_id = c.id 
        WHERE i.user_id = ? AND i.status = 'sent' AND i.due_date < CURDATE()
        ORDER BY i.due_date ASC 
        LIMIT 5
    ");
    $overdueInvoices->execute([$user_id]);
    $overdue = $overdueInvoices->fetchAll();
    
    // إحصائيات شهرية للرسم البياني
    $monthlyStats = $pdo->prepare("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as invoice_count,
            SUM(total_amount) as total_amount
        FROM invoices 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month ASC
    ");
    $monthlyStats->execute([$user_id]);
    $monthly = $monthlyStats->fetchAll();
    
} catch (Exception $e) {
    // في حالة حدوث خطأ، نعين قيم افتراضية
    $stats = [
        'total_invoices' => 0,
        'paid_invoices' => 0,
        'sent_invoices' => 0,
        'draft_invoices' => 0,
        'overdue_invoices' => 0,
        'total_revenue' => 0,
        'pending_amount' => 0
    ];
    $clientCount = 0;
    $recent = [];
    $overdue = [];
    $monthly = [];

    // تسجيل الخطأ (اختياري)
    error_log("Dashboard Error: " . $e->getMessage());
}

$page_title = 'لوحة التحكم';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-card p-4">
                <h2 class="mb-2">مرحباً، <?php echo htmlspecialchars($user['first_name']); ?>!</h2>
                <p class="text-muted mb-0">إليك نظرة سريعة على أداء فواتيرك</p>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card bg-primary">
                <div class="stats-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number"><?php echo number_format($stats['total_invoices']); ?></div>
                    <div class="stats-label">إجمالي الفواتير</div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card bg-success">
                <div class="stats-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number"><?php echo number_format($stats['paid_invoices']); ?></div>
                    <div class="stats-label">فواتير مدفوعة</div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card bg-warning">
                <div class="stats-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number"><?php echo number_format($stats['sent_invoices']); ?></div>
                    <div class="stats-label">فواتير معلقة</div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stats-card bg-info">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number"><?php echo number_format($clientCount); ?></div>
                    <div class="stats-label">العملاء</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Revenue Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-6">
            <div class="revenue-card">
                <div class="revenue-header">
                    <h5><i class="fas fa-dollar-sign me-2"></i>إجمالي الإيرادات</h5>
                </div>
                <div class="revenue-amount text-success">
                    <?php echo formatCurrency($stats['total_revenue']); ?>
                </div>
                <small class="text-muted">من الفواتير المدفوعة</small>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="revenue-card">
                <div class="revenue-header">
                    <h5><i class="fas fa-hourglass-half me-2"></i>المبالغ المعلقة</h5>
                </div>
                <div class="revenue-amount text-warning">
                    <?php echo formatCurrency($stats['pending_amount']); ?>
                </div>
                <small class="text-muted">من الفواتير غير المدفوعة</small>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="create-invoice.php" class="quick-action-btn">
                                <i class="fas fa-plus-circle"></i>
                                <span>إنشاء فاتورة جديدة</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="clients.php?action=add" class="quick-action-btn">
                                <i class="fas fa-user-plus"></i>
                                <span>إضافة عميل جديد</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="templates.php" class="quick-action-btn">
                                <i class="fas fa-th-large"></i>
                                <span>تصفح القوالب</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="reports.php" class="quick-action-btn">
                                <i class="fas fa-chart-bar"></i>
                                <span>عرض التقارير</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row g-4">
        <!-- Recent Invoices -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-file-invoice me-2"></i>الفواتير الأخيرة</h5>
                    <a href="invoices.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                            <h6>لا توجد فواتير بعد</h6>
                            <p class="text-muted">ابدأ بإنشاء فاتورتك الأولى</p>
                            <a href="create-invoice.php" class="btn btn-primary">إنشاء فاتورة</a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent as $invoice): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($invoice['client_name'] ?? 'غير محدد'); ?></td>
                                            <td><?php echo formatCurrency($invoice['total_amount'] ?? 0); ?></td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'draft' => 'secondary',
                                                    'sent' => 'warning',
                                                    'paid' => 'success',
                                                    'overdue' => 'danger',
                                                    'cancelled' => 'dark'
                                                ];
                                                $status_names = [
                                                    'draft' => 'مسودة',
                                                    'sent' => 'مرسلة',
                                                    'paid' => 'مدفوعة',
                                                    'overdue' => 'متأخرة',
                                                    'cancelled' => 'ملغاة'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_classes[$invoice['status']]; ?>">
                                                    <?php echo $status_names[$invoice['status']]; ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($invoice['created_at'], 'd/m/Y'); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="view-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                                       class="btn btn-outline-primary" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                                       class="btn btn-outline-secondary" title="تحرير">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Overdue Invoices & Chart -->
        <div class="col-lg-4">
            <!-- Overdue Invoices -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0 text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>فواتير متأخرة
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($overdue)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted mb-0">لا توجد فواتير متأخرة</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($overdue as $invoice): ?>
                            <div class="overdue-item">
                                <div class="d-flex justify-content-between">
                                    <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                    <span class="text-danger"><?php echo formatCurrency($invoice['total_amount'] ?? 0); ?></span>
                                </div>
                                <small class="text-muted">
                                    <?php echo htmlspecialchars($invoice['client_name'] ?? 'غير محدد'); ?> -
                                    متأخرة منذ <?php echo formatDate($invoice['due_date'] ?? '', 'd/m/Y'); ?>
                                </small>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center mt-3">
                            <a href="invoices.php?status=overdue" class="btn btn-sm btn-outline-danger">
                                عرض جميع المتأخرة
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Monthly Chart -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>الإحصائيات الشهرية</h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.welcome-card {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px;
}

.stats-card {
    color: white;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.revenue-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.revenue-amount {
    font-size: 2rem;
    font-weight: bold;
    margin: 15px 0 5px;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-2px);
}

.quick-action-btn i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.overdue-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.overdue-item:last-child {
    border-bottom: none;
}
</style>

<script>
// رسم بياني للإحصائيات الشهرية
const monthlyData = <?php echo json_encode($monthly); ?>;
const ctx = document.getElementById('monthlyChart').getContext('2d');

const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' });
        }),
        datasets: [{
            label: 'عدد الفواتير',
            data: monthlyData.map(item => item.invoice_count),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
