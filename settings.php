<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $pdo = getDBConnection();
        
        if ($action === 'profile') {
            // تحديث الملف الشخصي
            $first_name = sanitize($_POST['first_name']);
            $last_name = sanitize($_POST['last_name']);
            $company_name = sanitize($_POST['company_name']);
            $phone = sanitize($_POST['phone']);
            $address = sanitize($_POST['address']);
            $city = sanitize($_POST['city']);
            $country = sanitize($_POST['country']);
            
            $stmt = $pdo->prepare("
                UPDATE users 
                SET first_name = ?, last_name = ?, company_name = ?, phone = ?, 
                    address = ?, city = ?, country = ?, updated_at = NOW()
                WHERE id = ?
            ");
            
            if ($stmt->execute([$first_name, $last_name, $company_name, $phone, $address, $city, $country, $user_id])) {
                setMessage('تم تحديث الملف الشخصي بنجاح', 'success');
            } else {
                setMessage('حدث خطأ في تحديث الملف الشخصي', 'error');
            }
            
        } elseif ($action === 'password') {
            // تغيير كلمة المرور
            $current_password = $_POST['current_password'];
            $new_password = $_POST['new_password'];
            $confirm_password = $_POST['confirm_password'];
            
            if (!password_verify($current_password, $user['password'])) {
                setMessage('كلمة المرور الحالية غير صحيحة', 'error');
            } elseif ($new_password !== $confirm_password) {
                setMessage('كلمة المرور الجديدة وتأكيدها غير متطابقتان', 'error');
            } elseif (strlen($new_password) < 6) {
                setMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
            } else {
                $hashed_password = password_hash($new_password, HASH_ALGO);
                $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                
                if ($stmt->execute([$hashed_password, $user_id])) {
                    setMessage('تم تغيير كلمة المرور بنجاح', 'success');
                } else {
                    setMessage('حدث خطأ في تغيير كلمة المرور', 'error');
                }
            }
            
        } elseif ($action === 'invoice_settings') {
            // إعدادات الفواتير
            $settings = [
                'default_currency' => sanitize($_POST['default_currency']),
                'default_tax_rate' => (float)($_POST['default_tax_rate'] ?? 0),
                'invoice_prefix' => sanitize($_POST['invoice_prefix']),
                'invoice_footer' => sanitize($_POST['invoice_footer']),
                'payment_terms' => sanitize($_POST['payment_terms']),
                'late_fee_rate' => (float)($_POST['late_fee_rate'] ?? 0)
            ];
            
            foreach ($settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO user_settings (user_id, setting_key, setting_value) 
                    VALUES (?, ?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                ");
                $stmt->execute([$user_id, $key, $value]);
            }
            
            setMessage('تم حفظ إعدادات الفواتير بنجاح', 'success');
        }
        
        redirect(APP_URL . '/settings.php');
        
    } catch (Exception $e) {
        setMessage('حدث خطأ في النظام', 'error');
    }
}

// جلب الإعدادات الحالية
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM user_settings WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $settings_data = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (Exception $e) {
    $settings_data = [];
}

$page_title = 'الإعدادات';
include 'includes/header.php';
?>

<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="settings-sidebar">
                <h5 class="sidebar-title">الإعدادات</h5>
                <nav class="nav nav-pills flex-column">
                    <a class="nav-link active" href="#profile" data-bs-toggle="pill">
                        <i class="fas fa-user me-2"></i>الملف الشخصي
                    </a>
                    <a class="nav-link" href="#password" data-bs-toggle="pill">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </a>
                    <a class="nav-link" href="#invoice-settings" data-bs-toggle="pill">
                        <i class="fas fa-file-invoice me-2"></i>إعدادات الفواتير
                    </a>
                    <a class="nav-link" href="#notifications" data-bs-toggle="pill">
                        <i class="fas fa-bell me-2"></i>الإشعارات
                    </a>
                    <a class="nav-link" href="#subscription" data-bs-toggle="pill">
                        <i class="fas fa-crown me-2"></i>الاشتراك
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Content -->
        <div class="col-lg-9">
            <div class="tab-content">
                <!-- Profile Settings -->
                <div class="tab-pane fade show active" id="profile">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>الملف الشخصي</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="profile">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">الاسم الأول</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">الاسم الأخير</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" 
                                           value="<?php echo htmlspecialchars($user['email']); ?>" disabled>
                                    <div class="form-text">لا يمكن تغيير البريد الإلكتروني</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">اسم الشركة</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="<?php echo htmlspecialchars($user['company_name']); ?>">
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($user['phone']); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="city" class="form-label">المدينة</label>
                                        <input type="text" class="form-control" id="city" name="city" 
                                               value="<?php echo htmlspecialchars($user['city']); ?>">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="country" class="form-label">الدولة</label>
                                        <input type="text" class="form-control" id="country" name="country" 
                                               value="<?php echo htmlspecialchars($user['country']); ?>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($user['address']); ?></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ التغييرات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Password Settings -->
                <div class="tab-pane fade" id="password">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-lock me-2"></i>تغيير كلمة المرور</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="password">
                                
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" 
                                           minlength="6" required>
                                    <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           minlength="6" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Invoice Settings -->
                <div class="tab-pane fade" id="invoice-settings">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-file-invoice me-2"></i>إعدادات الفواتير</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="invoice_settings">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="default_currency" class="form-label">العملة الافتراضية</label>
                                        <select class="form-select" id="default_currency" name="default_currency">
                                            <option value="SAR" <?php echo ($settings_data['default_currency'] ?? 'SAR') === 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                            <option value="USD" <?php echo ($settings_data['default_currency'] ?? '') === 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                            <option value="EUR" <?php echo ($settings_data['default_currency'] ?? '') === 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="default_tax_rate" class="form-label">معدل الضريبة الافتراضي (%)</label>
                                        <input type="number" class="form-control" id="default_tax_rate" name="default_tax_rate" 
                                               value="<?php echo $settings_data['default_tax_rate'] ?? '15'; ?>" 
                                               min="0" max="100" step="0.01">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="invoice_prefix" class="form-label">بادئة رقم الفاتورة</label>
                                        <input type="text" class="form-control" id="invoice_prefix" name="invoice_prefix" 
                                               value="<?php echo htmlspecialchars($settings_data['invoice_prefix'] ?? 'INV-'); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="late_fee_rate" class="form-label">رسوم التأخير (%)</label>
                                        <input type="number" class="form-control" id="late_fee_rate" name="late_fee_rate" 
                                               value="<?php echo $settings_data['late_fee_rate'] ?? '0'; ?>" 
                                               min="0" max="100" step="0.01">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="payment_terms" class="form-label">شروط الدفع</label>
                                    <textarea class="form-control" id="payment_terms" name="payment_terms" rows="3"><?php echo htmlspecialchars($settings_data['payment_terms'] ?? 'الدفع خلال 30 يوم من تاريخ الفاتورة'); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="invoice_footer" class="form-label">تذييل الفاتورة</label>
                                    <textarea class="form-control" id="invoice_footer" name="invoice_footer" rows="3"><?php echo htmlspecialchars($settings_data['invoice_footer'] ?? 'شكراً لتعاملكم معنا'); ?></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ الإعدادات
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Notifications Settings -->
                <div class="tab-pane fade" id="notifications">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-bell me-2"></i>إعدادات الإشعارات</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                ستتوفر إعدادات الإشعارات في التحديث القادم
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_notifications" checked disabled>
                                <label class="form-check-label" for="email_notifications">
                                    إشعارات البريد الإلكتروني
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="invoice_reminders" checked disabled>
                                <label class="form-check-label" for="invoice_reminders">
                                    تذكير بالفواتير المستحقة
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="payment_notifications" checked disabled>
                                <label class="form-check-label" for="payment_notifications">
                                    إشعارات الدفع
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Settings -->
                <div class="tab-pane fade" id="subscription">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-crown me-2"></i>الاشتراك والفوترة</h5>
                        </div>
                        <div class="card-body">
                            <div class="subscription-info">
                                <div class="current-plan">
                                    <h6>الخطة الحالية</h6>
                                    <div class="plan-badge">
                                        <span class="badge bg-<?php echo $user['subscription_plan'] === 'free' ? 'secondary' : 'primary'; ?> fs-6">
                                            <?php
                                            $plan_names = [
                                                'free' => 'مجانية',
                                                'basic' => 'أساسية',
                                                'premium' => 'متقدمة',
                                                'enterprise' => 'مؤسسية'
                                            ];
                                            echo $plan_names[$user['subscription_plan']] ?? $user['subscription_plan'];
                                            ?>
                                        </span>
                                    </div>

                                    <?php if ($user['subscription_expires_at']): ?>
                                        <p class="text-muted mt-2">
                                            ينتهي في: <?php echo formatDate($user['subscription_expires_at'], 'd/m/Y'); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>

                                <div class="plan-features mt-4">
                                    <h6>مميزات خطتك الحالية:</h6>
                                    <ul class="list-unstyled">
                                        <?php if ($user['subscription_plan'] === 'free'): ?>
                                            <li><i class="fas fa-check text-success me-2"></i>5 فواتير شهرياً</li>
                                            <li><i class="fas fa-check text-success me-2"></i>3 قوالب أساسية</li>
                                            <li><i class="fas fa-times text-muted me-2"></i>التقارير المتقدمة</li>
                                        <?php else: ?>
                                            <li><i class="fas fa-check text-success me-2"></i>فواتير غير محدودة</li>
                                            <li><i class="fas fa-check text-success me-2"></i>جميع القوالب</li>
                                            <li><i class="fas fa-check text-success me-2"></i>التقارير المتقدمة</li>
                                            <li><i class="fas fa-check text-success me-2"></i>الدعم المتقدم</li>
                                        <?php endif; ?>
                                    </ul>
                                </div>

                                <?php if ($user['subscription_plan'] === 'free'): ?>
                                    <div class="upgrade-section mt-4">
                                        <h6>ترقية الاشتراك</h6>
                                        <p class="text-muted">احصل على مميزات أكثر مع الخطط المدفوعة</p>
                                        <a href="pricing.php" class="btn btn-primary">
                                            <i class="fas fa-arrow-up me-1"></i>ترقية الآن
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.settings-sidebar {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.sidebar-title {
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
}

.nav-pills .nav-link {
    color: #6c757d;
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    border: none;
}

.nav-pills .nav-link:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.nav-pills .nav-link.active {
    background-color: #007bff;
    color: white;
}

.nav-pills .nav-link i {
    width: 20px;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
}

.subscription-info {
    text-align: center;
}

.current-plan {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 20px;
}

.plan-badge .badge {
    padding: 8px 16px;
    font-size: 1rem;
}

.plan-features {
    text-align: right;
}

.plan-features ul {
    max-width: 300px;
    margin: 0 auto;
}

.upgrade-section {
    padding: 20px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px;
}

.upgrade-section .btn {
    background: white;
    color: #007bff;
    border: none;
}

.upgrade-section .btn:hover {
    background: #f8f9fa;
}

@media (max-width: 768px) {
    .settings-sidebar {
        position: static;
        margin-bottom: 20px;
    }

    .nav-pills {
        flex-direction: row;
        overflow-x: auto;
        white-space: nowrap;
    }

    .nav-pills .nav-link {
        flex-shrink: 0;
        margin-left: 5px;
    }
}
</style>

<script>
// التحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;

    if (newPassword !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// حفظ التبويب النشط
document.addEventListener('DOMContentLoaded', function() {
    const hash = window.location.hash;
    if (hash) {
        const tabTrigger = document.querySelector(`[href="${hash}"]`);
        if (tabTrigger) {
            const tab = new bootstrap.Tab(tabTrigger);
            tab.show();
        }
    }
});

// تحديث الرابط عند تغيير التبويب
document.querySelectorAll('[data-bs-toggle="pill"]').forEach(function(tab) {
    tab.addEventListener('shown.bs.tab', function(e) {
        window.location.hash = e.target.getAttribute('href');
    });
});
</script>

<?php include 'includes/footer.php'; ?>
