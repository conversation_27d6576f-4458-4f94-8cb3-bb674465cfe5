<?php
// اختبار نهائي بسيط لصفحة العملاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار نهائي - صفحة العملاء</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-warning { background: #ffc107; color: #212529; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🎯 اختبار نهائي - صفحة العملاء</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// اختبار الاتصال بقاعدة البيانات
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// فحص جدول العملاء
echo "<h2>فحص جدول العملاء</h2>";
try {
    $stmt = $pdo->query("DESCRIBE clients");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $hasUserId = in_array('user_id', $columns);
    
    if ($hasUserId) {
        echo "<div class='result success'>✅ جدول العملاء صحيح وعمود user_id موجود</div>";
        echo "<div class='result info'>الأعمدة الموجودة: " . implode(', ', $columns) . "</div>";
    } else {
        echo "<div class='result error'>❌ عمود user_id مفقود من جدول العملاء</div>";
        echo "<div class='result warning'>يجب تشغيل الإصلاح الجذري أولاً</div>";
        echo "<a href='deep_fix_clients.php' class='btn btn-warning'>🔧 إصلاح جذري للجدول</a>";
        exit;
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</div>";
    
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        echo "<div class='result warning'>جدول العملاء غير موجود</div>";
        echo "<a href='deep_fix_clients.php' class='btn btn-warning'>🔧 إنشاء جدول العملاء</a>";
        exit;
    }
}

// محاكاة تسجيل الدخول
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    try {
        $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
        $user = $stmt->fetch();
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            echo "<div class='result success'>✅ تم تسجيل الدخول للمستخدم ID: " . $user['id'] . "</div>";
        } else {
            echo "<div class='result error'>❌ لا يوجد مستخدمين نشطين</div>";
            exit;
        }
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في تسجيل الدخول: " . $e->getMessage() . "</div>";
        exit;
    }
}

$user_id = $_SESSION['user_id'];

// اختبار استعلام العملاء
echo "<h2>اختبار استعلام العملاء</h2>";
try {
    // اختبار بسيط أولاً
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $count = $stmt->fetchColumn();
    
    echo "<div class='result success'>✅ الاستعلام البسيط نجح: $count عميل</div>";
    
    if ($count == 0) {
        echo "<div class='result warning'>⚠️ لا يوجد عملاء - سيتم إضافة عملاء تجريبيين</div>";
        echo "<a href='deep_fix_clients.php' class='btn btn-warning'>🔧 إضافة بيانات تجريبية</a>";
    } else {
        // اختبار الاستعلام المعقد
        $sql = "
            SELECT c.*, 
                   COALESCE(COUNT(i.id), 0) as invoice_count
            FROM clients c 
            LEFT JOIN invoices i ON c.id = i.client_id
            WHERE c.user_id = ? AND c.is_active = 1
            GROUP BY c.id
            ORDER BY c.name ASC 
            LIMIT 5
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$user_id]);
        $clients = $stmt->fetchAll();
        
        echo "<div class='result success'>✅ الاستعلام المعقد نجح: " . count($clients) . " عميل</div>";
        
        if (!empty($clients)) {
            echo "<div class='result info'>";
            echo "<strong>عينة من العملاء:</strong><br>";
            foreach ($clients as $client) {
                echo "- " . htmlspecialchars($client['name']) . " (" . htmlspecialchars($client['email'] ?? 'بدون بريد') . ")<br>";
            }
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في استعلام العملاء: " . $e->getMessage() . "</div>";
    
    if (strpos($e->getMessage(), 'user_id') !== false) {
        echo "<div class='result warning'>المشكلة في عمود user_id</div>";
        echo "<a href='deep_fix_clients.php' class='btn btn-warning'>🔧 إصلاح المشكلة</a>";
    }
}

// اختبار صفحة العملاء
echo "<h2>اختبار صفحة العملاء</h2>";
try {
    // محاولة تحميل صفحة العملاء
    ob_start();
    $error_occurred = false;
    
    try {
        // محاكاة جزء من كود clients.php
        $search = '';
        $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
        $params = [$user_id];
        $where_clause = implode(' AND ', $where_conditions);
        
        $countSql = "SELECT COUNT(*) FROM clients c WHERE $where_clause";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total_clients = $countStmt->fetchColumn();
        
        echo "<div class='result success'>✅ كود صفحة العملاء يعمل بشكل صحيح</div>";
        echo "<div class='result info'>إجمالي العملاء: $total_clients</div>";
        
    } catch (Exception $e) {
        $error_occurred = true;
        echo "<div class='result error'>❌ خطأ في كود صفحة العملاء: " . $e->getMessage() . "</div>";
    }
    
    ob_end_clean();
    
    if (!$error_occurred) {
        echo "<div class='result success'>✅ صفحة العملاء جاهزة للعمل!</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار الصفحة: " . $e->getMessage() . "</div>";
}

echo "<h2>النتيجة النهائية</h2>";

if ($hasUserId && !$error_occurred) {
    echo "<div class='result success'>";
    echo "<h3>🎉 صفحة العملاء تعمل بشكل مثالي!</h3>";
    echo "<p>جميع الاختبارات نجحت والصفحة جاهزة للاستخدام</p>";
    echo "</div>";
    
    echo "<h3>اختبار الصفحات:</h3>";
    echo "<a href='clients.php' class='btn btn-success' target='_blank'>🔗 صفحة العملاء</a>";
    echo "<a href='dashboard.php' class='btn' target='_blank'>🔗 لوحة التحكم</a>";
    echo "<a href='create-invoice.php' class='btn' target='_blank'>🔗 إنشاء فاتورة</a>";
    
} else {
    echo "<div class='result warning'>";
    echo "<h3>⚠️ هناك مشاكل تحتاج للحل</h3>";
    echo "<p>يرجى تشغيل الإصلاح الجذري أولاً</p>";
    echo "</div>";
    
    echo "<h3>الإصلاح المطلوب:</h3>";
    echo "<a href='deep_fix_clients.php' class='btn btn-warning'>🔧 إصلاح جذري لجدول العملاء</a>";
}

echo "<div class='result info'>";
echo "<strong>بيانات الدخول التجريبية:</strong><br>";
echo "اسم المستخدم: admin<br>";
echo "كلمة المرور: 123456";
echo "</div>";

echo "</body></html>";
?>
