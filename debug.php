<?php
// ملف تشخيص الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>تشخيص النظام</h1>";

// فحص إصدار PHP
echo "<h2>معلومات PHP</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "نظام التشغيل: " . PHP_OS . "<br>";

// فحص الامتدادات المطلوبة
echo "<h2>الامتدادات المطلوبة</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'session', 'curl'];

foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ متوفر' : '❌ غير متوفر';
    echo "$ext: $status<br>";
}

// فحص الملفات
echo "<h2>فحص الملفات</h2>";
$files_to_check = [
    'config/config.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($files_to_check as $file) {
    $status = file_exists($file) ? '✅ موجود' : '❌ غير موجود';
    echo "$file: $status<br>";
}

// فحص الصلاحيات
echo "<h2>فحص الصلاحيات</h2>";
$dirs_to_check = [
    'assets/uploads',
    'assets/temp',
    'logs'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة';
        echo "$dir: $writable<br>";
    } else {
        echo "$dir: ❌ المجلد غير موجود<br>";
    }
}

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات</h2>";
if (file_exists('config/config.php')) {
    try {
        require_once 'config/config.php';
        
        if (defined('DB_HOST')) {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "✅ الاتصال بقاعدة البيانات نجح<br>";
            
            // فحص الجداول
            $stmt = $pdo->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "عدد الجداول: " . count($tables) . "<br>";
            
        } else {
            echo "❌ ملف التكوين غير مكتمل<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ ملف التكوين غير موجود<br>";
}

// معلومات الخادم
echo "<h2>معلومات الخادم</h2>";
echo "خادم الويب: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "المجلد الجذر: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "المجلد الحالي: " . getcwd() . "<br>";

// فحص .htaccess
echo "<h2>فحص .htaccess</h2>";
if (file_exists('.htaccess')) {
    echo "✅ ملف .htaccess موجود<br>";
    $htaccess_size = filesize('.htaccess');
    echo "حجم الملف: $htaccess_size بايت<br>";
} else {
    echo "❌ ملف .htaccess غير موجود<br>";
}

echo "<h2>اختبار بسيط</h2>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";
echo "المنطقة الزمنية: " . date_default_timezone_get() . "<br>";

phpinfo();
?>
