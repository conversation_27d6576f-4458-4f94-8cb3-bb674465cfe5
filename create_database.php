<?php
// ملف إنشاء قاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>إنشاء قاعدة البيانات</h1>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ الاتصال بـ MySQL نجح<br>";
    
    // إنشاء قاعدة البيانات
    $dbname = 'invoice_saas';
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات '$dbname'<br>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء جدول المستخدمين
    $sql = "
    CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        company_name VARCHAR(100),
        phone VARCHAR(20),
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50),
        subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
        subscription_expires_at DATETIME NULL,
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول المستخدمين<br>";
    
    // إنشاء جدول العملاء
    $sql = "
    CREATE TABLE IF NOT EXISTS clients (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        company VARCHAR(100),
        address TEXT,
        city VARCHAR(50),
        country VARCHAR(50),
        tax_number VARCHAR(50),
        notes TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول العملاء<br>";
    
    // إنشاء جدول الفواتير
    $sql = "
    CREATE TABLE IF NOT EXISTS invoices (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        client_id INT NOT NULL,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        title VARCHAR(255),
        issue_date DATE NOT NULL,
        due_date DATE,
        status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
        subtotal DECIMAL(10,2) DEFAULT 0,
        tax_rate DECIMAL(5,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'USD',
        notes TEXT,
        terms TEXT,
        template_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول الفواتير<br>";
    
    // إنشاء جدول عناصر الفواتير
    $sql = "
    CREATE TABLE IF NOT EXISTS invoice_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        invoice_id INT NOT NULL,
        description TEXT NOT NULL,
        quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول عناصر الفواتير<br>";
    
    // إنشاء جدول سجل الأنشطة
    $sql = "
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT,
        action VARCHAR(50) NOT NULL,
        entity_type VARCHAR(50),
        entity_id INT,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول سجل الأنشطة<br>";
    
    // إنشاء مستخدم تجريبي
    $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
    $sql = "
    INSERT IGNORE INTO users (username, email, password, first_name, last_name, company_name) 
    VALUES ('admin', '<EMAIL>', ?, 'المدير', 'العام', 'شركة تجريبية')
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$hashedPassword]);
    echo "✅ تم إنشاء مستخدم تجريبي (admin / 123456)<br>";
    
    echo "<h2>تم الانتهاء بنجاح!</h2>";
    echo "<p>يمكنك الآن:</p>";
    echo "<ul>";
    echo "<li><a href='test.php'>اختبار النظام</a></li>";
    echo "<li><a href='index.php'>الذهاب للصفحة الرئيسية</a></li>";
    echo "<li><a href='auth/login.php'>تسجيل الدخول</a> (admin / 123456)</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
    echo "<h3>تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL في XAMPP/WAMP</li>";
    echo "<li>صحة بيانات الاتصال في config/config.php</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
h1, h2 {
    color: #333;
}
ul {
    text-align: right;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
