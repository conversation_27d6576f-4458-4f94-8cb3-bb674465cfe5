<?php
// Final test to verify all duplicate function errors are resolved
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Final System Test</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }";
echo ".error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }";
echo ".info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }";
echo "h1 { color: #333; }";
echo ".test-links a { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 Final System Test</h1>";

$allGood = true;

// Test loading all files without errors
echo "<h2>Testing File Loading (No Duplicate Function Errors)</h2>";

try {
    // Test config.php
    require_once 'config/config.php';
    echo "<div class='result success'>✅ config/config.php loaded successfully</div>";
    
    // Test includes/functions.php
    require_once 'includes/functions.php';
    echo "<div class='result success'>✅ includes/functions.php loaded successfully</div>";
    
    echo "<div class='result success'>✅ NO DUPLICATE FUNCTION ERRORS FOUND!</div>";
    
} catch (Error $e) {
    if (strpos($e->getMessage(), 'Cannot redeclare') !== false) {
        echo "<div class='result error'>❌ DUPLICATE FUNCTION ERROR: " . htmlspecialchars($e->getMessage()) . "</div>";
        $allGood = false;
    } else {
        echo "<div class='result error'>❌ OTHER ERROR: " . htmlspecialchars($e->getMessage()) . "</div>";
        $allGood = false;
    }
} catch (Exception $e) {
    echo "<div class='result error'>❌ EXCEPTION: " . htmlspecialchars($e->getMessage()) . "</div>";
    $allGood = false;
}

// Test core functions
echo "<h2>Testing Core Functions</h2>";
$coreFunctions = [
    'isLoggedIn' => 'Check login status',
    'sanitize' => 'Sanitize input data', 
    'redirect' => 'Redirect users',
    'getDBConnection' => 'Database connection',
    'setMessage' => 'Set flash messages',
    'getMessage' => 'Get flash messages',
    'formatCurrency' => 'Format currency',
    'formatDate' => 'Format dates'
];

foreach ($coreFunctions as $func => $description) {
    if (function_exists($func)) {
        echo "<div class='result success'>✅ $func() - $description</div>";
    } else {
        echo "<div class='result error'>❌ $func() - $description (MISSING)</div>";
        $allGood = false;
    }
}

// Test additional functions
echo "<h2>Testing Additional Functions</h2>";
$additionalFunctions = [
    'generateInvoiceNumber' => 'Generate invoice numbers',
    'isValidEmail' => 'Validate email addresses',
    'hashPassword' => 'Hash passwords',
    'verifyPassword' => 'Verify passwords',
    'generateToken' => 'Generate random tokens',
    'generateCSRFToken' => 'Generate CSRF tokens'
];

foreach ($additionalFunctions as $func => $description) {
    if (function_exists($func)) {
        echo "<div class='result success'>✅ $func() - $description</div>";
    } else {
        echo "<div class='result error'>❌ $func() - $description (MISSING)</div>";
        $allGood = false;
    }
}

// Test database connection
echo "<h2>Testing Database</h2>";
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ Database connection successful</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
    $allGood = false;
}

// Final result
echo "<h2>Final Result</h2>";
if ($allGood) {
    echo "<div class='result success'>";
    echo "<h3>🎉 ALL TESTS PASSED!</h3>";
    echo "<p><strong>✅ No duplicate function errors</strong></p>";
    echo "<p><strong>✅ All core functions working</strong></p>";
    echo "<p><strong>✅ Database connection successful</strong></p>";
    echo "<p><strong>✅ System is ready for use</strong></p>";
    echo "</div>";
} else {
    echo "<div class='result error'>";
    echo "<h3>❌ Some Issues Found</h3>";
    echo "<p>Please review the errors above.</p>";
    echo "</div>";
}

// Test links
echo "<h2>Test the System</h2>";
echo "<div class='test-links'>";
echo "<a href='index.php' target='_blank'>🏠 Homepage</a>";
echo "<a href='contact.php' target='_blank'>📧 Contact Form</a>";
echo "<a href='auth/login.php' target='_blank'>🔐 Login (admin/123456)</a>";
echo "<a href='templates.php' target='_blank'>🎨 Templates</a>";
echo "<a href='pricing.php' target='_blank'>💰 Pricing</a>";
echo "<a href='help.php' target='_blank'>❓ Help</a>";
echo "</div>";

echo "<div class='result info'>";
echo "<h3>🔧 What Was Fixed:</h3>";
echo "<ul>";
echo "<li>Removed duplicate <code>setMessage()</code> function from includes/functions.php</li>";
echo "<li>Removed duplicate <code>getMessage()</code> function from includes/functions.php</li>";
echo "<li>Removed duplicate <code>formatCurrency()</code> function from includes/functions.php</li>";
echo "<li>Removed duplicate <code>formatDate()</code> function from includes/functions.php</li>";
echo "<li>Kept all core functions only in config/config.php</li>";
echo "<li>Kept only unique utility functions in includes/functions.php</li>";
echo "</ul>";
echo "</div>";

echo "<div class='result info'>";
echo "<strong>Demo Login:</strong><br>";
echo "Username: <code>admin</code><br>";
echo "Password: <code>123456</code>";
echo "</div>";

echo "</body></html>";
?>
