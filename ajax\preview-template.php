<?php
require_once '../config/config.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo '<div class="alert alert-danger">معرف القالب غير صحيح</div>';
    exit;
}

$template_id = (int)$_GET['id'];

try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM invoice_templates WHERE id = ? AND is_active = 1");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch();
    
    if (!$template) {
        echo '<div class="alert alert-danger">القالب غير موجود</div>';
        exit;
    }
    
    // بيانات تجريبية للمعاينة
    $sample_data = [
        'invoice_number' => 'INV-2024-001',
        'issue_date' => date('Y-m-d'),
        'due_date' => date('Y-m-d', strtotime('+30 days')),
        'company_name' => 'شركة الأعمال المتقدمة',
        'company_address' => 'الرياض، المملكة العربية السعودية<br>ص.ب: 12345<br>هاتف: +966 11 123 4567',
        'client_name' => 'شركة العميل المميز',
        'client_address' => 'جدة، المملكة العربية السعودية<br>ص.ب: 67890<br>هاتف: +966 12 987 6543',
        'subtotal' => '1,500.00 ريال',
        'tax_amount' => '225.00 ريال',
        'total_amount' => '1,725.00 ريال'
    ];
    
    // عناصر تجريبية للفاتورة
    $sample_items = [
        ['description' => 'خدمات استشارية', 'quantity' => '10', 'price' => '100.00', 'total' => '1,000.00'],
        ['description' => 'تطوير موقع إلكتروني', 'quantity' => '1', 'price' => '500.00', 'total' => '500.00']
    ];
    
    // إنشاء HTML للعناصر
    $items_html = '';
    foreach ($sample_items as $item) {
        $items_html .= '<tr>';
        $items_html .= '<td>' . $item['description'] . '</td>';
        $items_html .= '<td>' . $item['quantity'] . '</td>';
        $items_html .= '<td>' . $item['price'] . ' ريال</td>';
        $items_html .= '<td>' . $item['total'] . ' ريال</td>';
        $items_html .= '</tr>';
    }
    
    // استبدال المتغيرات في القالب
    $html_content = $template['html_template'];
    foreach ($sample_data as $key => $value) {
        $html_content = str_replace('{{' . $key . '}}', $value, $html_content);
    }
    $html_content = str_replace('{{invoice_items}}', $items_html, $html_content);
    
    // إضافة CSS
    $css_content = $template['css_styles'];
    
    echo '<div class="template-preview-container">';
    echo '<style>' . $css_content . '</style>';
    echo $html_content;
    echo '</div>';
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">حدث خطأ في تحميل القالب</div>';
}
?>

<style>
.template-preview-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تحسينات إضافية للمعاينة */
.template-preview-container table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.template-preview-container th,
.template-preview-container td {
    padding: 8px 12px;
    text-align: right;
    border: 1px solid #ddd;
}

.template-preview-container th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.template-preview-container .invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.template-preview-container .parties {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

.template-preview-container .summary {
    text-align: left;
    margin-top: 20px;
}

.template-preview-container .summary-row {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
    padding: 5px 0;
}

.template-preview-container .total {
    font-weight: bold;
    font-size: 1.1em;
    border-top: 2px solid #007bff;
    padding-top: 10px;
    margin-top: 15px;
}
</style>
