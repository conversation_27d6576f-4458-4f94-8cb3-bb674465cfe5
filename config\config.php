<?php
/**
 * إعدادات التطبيق الرئيسية
 * Main Application Configuration
 */

// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// بدء الجلسة
session_start();

// إعدادات التطبيق
define('APP_NAME', 'Invoice SaaS');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/invoice');
define('APP_TIMEZONE', 'Asia/Riyadh');

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'invoice_saas');
define('DB_USER', 'root');
define('DB_PASS', '');

// إعدادات الأمان
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600); // ساعة واحدة

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// إعدادات العملة
define('DEFAULT_CURRENCY', 'USD');
define('CURRENCY_SYMBOL', '$');

// إعدادات الباقات
define('FREE_PLAN_INVOICES', 5);
define('BASIC_PLAN_INVOICES', 50);
define('PREMIUM_PLAN_INVOICES', 500);

// تعيين المنطقة الزمنية
date_default_timezone_set(APP_TIMEZONE);

// تعريف أن ملف التكوين تم تحميله
define('CONFIG_LOADED', true);

// تضمين ملف قاعدة البيانات
require_once 'database.php';

// تضمين ملف الدوال المساعدة
require_once __DIR__ . '/../includes/functions.php';

// دالة للحصول على اتصال قاعدة البيانات
function getDBConnection() {
    $database = new Database();
    return $database->connect();
}

// دالة لتنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة للحصول على المستخدم الحالي
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND is_active = 1");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// دالة لعرض الرسائل
function setMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

// دالة لتنسيق العملة
function formatCurrency($amount, $currency = DEFAULT_CURRENCY) {
    return number_format($amount, 2) . ' ' . $currency;
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = 'Y-m-d') {
    return date($format, strtotime($date));
}
?>
