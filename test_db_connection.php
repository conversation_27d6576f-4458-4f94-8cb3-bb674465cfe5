<?php
// اختبار سريع للاتصال بقاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار الاتصال بقاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔗 اختبار الاتصال بقاعدة البيانات</h1>";

// اختبار 1: تحميل التكوين
echo "<h2>1. تحميل التكوين</h2>";
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل ملف التكوين بنجاح</div>";
    
    echo "<div class='result info'>";
    echo "<strong>إعدادات قاعدة البيانات:</strong><br>";
    echo "المضيف: " . DB_HOST . "<br>";
    echo "قاعدة البيانات: " . DB_NAME . "<br>";
    echo "المستخدم: " . DB_USER . "<br>";
    echo "الترميز: " . DB_CHARSET . "<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في تحميل التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// اختبار 2: الاتصال بقاعدة البيانات
echo "<h2>2. الاتصال بقاعدة البيانات</h2>";
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT DATABASE() as current_db, VERSION() as mysql_version");
    $result = $stmt->fetch();
    
    echo "<div class='result info'>";
    echo "<strong>معلومات قاعدة البيانات:</strong><br>";
    echo "قاعدة البيانات الحالية: " . $result['current_db'] . "<br>";
    echo "إصدار MySQL: " . $result['mysql_version'] . "<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    echo "<div class='result info'>";
    echo "<strong>الحلول المقترحة:</strong><br>";
    echo "1. تأكد من تشغيل خادم MySQL<br>";
    echo "2. تحقق من بيانات الاتصال في config.php<br>";
    echo "3. شغل ملف fix_database_connection.php<br>";
    echo "</div>";
    exit;
}

// اختبار 3: فحص الجداول
echo "<h2>3. فحص الجداول</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<div class='result error'>❌ لا توجد جداول في قاعدة البيانات</div>";
        echo "<div class='result info'>يرجى تشغيل ملف fix_database_connection.php لإنشاء الجداول</div>";
    } else {
        echo "<div class='result success'>✅ تم العثور على " . count($tables) . " جدول</div>";
        echo "<div class='result info'>";
        echo "<strong>الجداول الموجودة:</strong><br>";
        foreach ($tables as $table) {
            echo "📋 $table<br>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجداول: " . $e->getMessage() . "</div>";
}

// اختبار 4: فحص المستخدمين
echo "<h2>4. فحص المستخدمين</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as user_count FROM users");
    $result = $stmt->fetch();
    
    if ($result['user_count'] == 0) {
        echo "<div class='result error'>❌ لا يوجد مستخدمين في قاعدة البيانات</div>";
        echo "<div class='result info'>يرجى تشغيل ملف fix_database_connection.php لإنشاء المستخدم التجريبي</div>";
    } else {
        echo "<div class='result success'>✅ يوجد " . $result['user_count'] . " مستخدم في قاعدة البيانات</div>";
        
        // عرض المستخدمين
        $stmt = $pdo->query("SELECT username, email, first_name, last_name FROM users LIMIT 5");
        $users = $stmt->fetchAll();
        
        echo "<div class='result info'>";
        echo "<strong>المستخدمين:</strong><br>";
        foreach ($users as $user) {
            echo "👤 " . $user['username'] . " (" . $user['first_name'] . " " . $user['last_name'] . ") - " . $user['email'] . "<br>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص المستخدمين: " . $e->getMessage() . "</div>";
}

// اختبار 5: اختبار تسجيل الدخول
echo "<h2>5. اختبار تسجيل الدخول</h2>";
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin' AND is_active = 1");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<div class='result success'>✅ المستخدم التجريبي 'admin' موجود ونشط</div>";
        
        // اختبار كلمة المرور
        if (password_verify('123456', $admin['password'])) {
            echo "<div class='result success'>✅ كلمة المرور '123456' صحيحة</div>";
        } else {
            echo "<div class='result error'>❌ كلمة المرور '123456' غير صحيحة</div>";
        }
        
    } else {
        echo "<div class='result error'>❌ المستخدم التجريبي 'admin' غير موجود أو غير نشط</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار تسجيل الدخول: " . $e->getMessage() . "</div>";
}

// النتيجة النهائية
echo "<h2>النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 اختبار الاتصال مكتمل!</h3>";
echo "<p>إذا رأيت هذه الرسالة، فإن الاتصال بقاعدة البيانات يعمل بشكل صحيح.</p>";
echo "</div>";

echo "<h3>الخطوات التالية:</h3>";
echo "<div class='result info'>";
echo "<ol>";
echo "<li><a href='fix_database_connection.php'>إصلاح قاعدة البيانات (إذا لزم الأمر)</a></li>";
echo "<li><a href='auth/login.php'>تجربة تسجيل الدخول</a></li>";
echo "<li><a href='index.php'>الذهاب للصفحة الرئيسية</a></li>";
echo "<li><a href='contact.php'>اختبار نموذج التواصل</a></li>";
echo "</ol>";
echo "</div>";

echo "<div class='result info'>";
echo "<strong>بيانات الدخول التجريبية:</strong><br>";
echo "اسم المستخدم: <code>admin</code><br>";
echo "كلمة المرور: <code>123456</code>";
echo "</div>";

echo "</body></html>";
?>
