<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $pdo = getDBConnection();
    
    // بناء استعلام البحث
    $where_conditions = ["i.user_id = ?"];
    $params = [$user_id];
    
    if (!empty($search)) {
        $where_conditions[] = "(i.invoice_number LIKE ? OR c.name LIKE ? OR i.title LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "i.status = ?";
        $params[] = $status_filter;
    }
    
    if (!empty($date_from)) {
        $where_conditions[] = "i.issue_date >= ?";
        $params[] = $date_from;
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "i.issue_date <= ?";
        $params[] = $date_to;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // عدد الفواتير الإجمالي
    $countSql = "SELECT COUNT(*) FROM invoices i LEFT JOIN clients c ON i.client_id = c.id WHERE $where_clause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total_invoices = $countStmt->fetchColumn();
    $total_pages = ceil($total_invoices / $per_page);
    
    // جلب الفواتير
    $sql = "
        SELECT i.*, c.name as client_name, c.email as client_email,
               CASE 
                   WHEN i.status = 'sent' AND i.due_date < CURDATE() THEN 'overdue'
                   ELSE i.status 
               END as display_status
        FROM invoices i 
        LEFT JOIN clients c ON i.client_id = c.id 
        WHERE $where_clause 
        ORDER BY i.created_at DESC 
        LIMIT $per_page OFFSET $offset
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $invoices = $stmt->fetchAll();
    
    // إحصائيات سريعة
    $statsStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft,
            COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid,
            COUNT(CASE WHEN status = 'sent' AND due_date < CURDATE() THEN 1 END) as overdue,
            SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_paid,
            SUM(CASE WHEN status != 'paid' THEN total_amount ELSE 0 END) as total_pending
        FROM invoices 
        WHERE user_id = ?
    ");
    $statsStmt->execute([$user_id]);
    $stats = $statsStmt->fetch();
    
} catch (Exception $e) {
    $invoices = [];
    $total_invoices = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'draft' => 0, 'sent' => 0, 'paid' => 0, 'overdue' => 0, 'total_paid' => 0, 'total_pending' => 0];
}

$page_title = 'إدارة الفواتير';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h2><i class="fas fa-file-invoice me-2"></i>إدارة الفواتير</h2>
            <p class="text-muted">إدارة وتتبع جميع فواتيرك</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="create-invoice.php" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
            </a>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-2">
            <div class="stats-card-small bg-primary">
                <div class="stats-number"><?php echo number_format($stats['total']); ?></div>
                <div class="stats-label">إجمالي</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card-small bg-secondary">
                <div class="stats-number"><?php echo number_format($stats['draft']); ?></div>
                <div class="stats-label">مسودات</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card-small bg-warning">
                <div class="stats-number"><?php echo number_format($stats['sent']); ?></div>
                <div class="stats-label">مرسلة</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card-small bg-success">
                <div class="stats-number"><?php echo number_format($stats['paid']); ?></div>
                <div class="stats-label">مدفوعة</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card-small bg-danger">
                <div class="stats-number"><?php echo number_format($stats['overdue']); ?></div>
                <div class="stats-label">متأخرة</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card-small bg-info">
                <div class="stats-number"><?php echo formatCurrency($stats['total_paid']); ?></div>
                <div class="stats-label">المحصل</div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" name="search" 
                           placeholder="رقم الفاتورة، العميل..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="draft" <?php echo $status_filter === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                        <option value="sent" <?php echo $status_filter === 'sent' ? 'selected' : ''; ?>>مرسلة</option>
                        <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>مدفوعة</option>
                        <option value="overdue" <?php echo $status_filter === 'overdue' ? 'selected' : ''; ?>>متأخرة</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغاة</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" name="date_from" 
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" name="date_to" 
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                        <a href="invoices.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Invoices Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">الفواتير (<?php echo number_format($total_invoices); ?>)</h5>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="exportInvoices()">
                    <i class="fas fa-download me-1"></i>تصدير
                </button>
                <button class="btn btn-outline-secondary" onclick="printInvoices()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($invoices)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <h5>لا توجد فواتير</h5>
                    <p class="text-muted">لم يتم العثور على فواتير تطابق معايير البحث</p>
                    <a href="create-invoice.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إنشاء فاتورة جديدة
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoices as $invoice): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input invoice-checkbox" 
                                               value="<?php echo $invoice['id']; ?>">
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                        <?php if ($invoice['title']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($invoice['title']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($invoice['client_name']); ?>
                                        <?php if ($invoice['client_email']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($invoice['client_email']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo formatCurrency($invoice['total_amount'], $invoice['currency']); ?></strong>
                                    </td>
                                    <td><?php echo formatDate($invoice['issue_date'], 'd/m/Y'); ?></td>
                                    <td>
                                        <?php if ($invoice['due_date']): ?>
                                            <?php echo formatDate($invoice['due_date'], 'd/m/Y'); ?>
                                            <?php if ($invoice['display_status'] === 'overdue'): ?>
                                                <br><small class="text-danger">متأخرة</small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'draft' => 'secondary',
                                            'sent' => 'warning',
                                            'paid' => 'success',
                                            'overdue' => 'danger',
                                            'cancelled' => 'dark'
                                        ];
                                        $status_names = [
                                            'draft' => 'مسودة',
                                            'sent' => 'مرسلة',
                                            'paid' => 'مدفوعة',
                                            'overdue' => 'متأخرة',
                                            'cancelled' => 'ملغاة'
                                        ];
                                        $display_status = $invoice['display_status'];
                                        ?>
                                        <span class="badge bg-<?php echo $status_classes[$display_status]; ?>">
                                            <?php echo $status_names[$display_status]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="view-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                               class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                               class="btn btn-outline-secondary" title="تحرير">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-info" 
                                                    onclick="duplicateInvoice(<?php echo $invoice['id']; ?>)" title="نسخ">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <div class="btn-group">
                                                <button class="btn btn-outline-success dropdown-toggle" 
                                                        data-bs-toggle="dropdown" title="المزيد">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="printInvoice(<?php echo $invoice['id']; ?>)">
                                                        <i class="fas fa-print me-2"></i>طباعة
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="downloadPDF(<?php echo $invoice['id']; ?>)">
                                                        <i class="fas fa-file-pdf me-2"></i>تحميل PDF
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="sendEmail(<?php echo $invoice['id']; ?>)">
                                                        <i class="fas fa-envelope me-2"></i>إرسال بالبريد
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteInvoice(<?php echo $invoice['id']; ?>)">
                                                        <i class="fas fa-trash me-2"></i>حذف
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="card-footer">
                        <nav aria-label="صفحات الفواتير">
                            <ul class="pagination justify-content-center mb-0">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.stats-card-small {
    color: white;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.stats-card-small .stats-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-card-small .stats-label {
    font-size: 0.85rem;
    opacity: 0.9;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.invoice-checkbox {
    cursor: pointer;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>

<script>
// تحديد/إلغاء تحديد جميع الفواتير
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.invoice-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// وظائف الإجراءات
function duplicateInvoice(invoiceId) {
    if (confirm('هل تريد إنشاء نسخة من هذه الفاتورة؟')) {
        window.location.href = 'create-invoice.php?duplicate=' + invoiceId;
    }
}

function printInvoice(invoiceId) {
    window.open('print-invoice.php?id=' + invoiceId, '_blank');
}

function downloadPDF(invoiceId) {
    window.location.href = 'export-pdf.php?id=' + invoiceId;
}

function sendEmail(invoiceId) {
    if (confirm('هل تريد إرسال الفاتورة بالبريد الإلكتروني؟')) {
        // سيتم تنفيذ هذه الوظيفة لاحقاً
        alert('سيتم إرسال الفاتورة قريباً');
    }
}

function deleteInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        // سيتم تنفيذ هذه الوظيفة لاحقاً
        alert('سيتم حذف الفاتورة قريباً');
    }
}

function exportInvoices() {
    const selected = Array.from(document.querySelectorAll('.invoice-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى اختيار فاتورة واحدة على الأقل للتصدير');
        return;
    }
    // سيتم تنفيذ التصدير لاحقاً
    alert('سيتم تصدير الفواتير قريباً');
}

function printInvoices() {
    const selected = Array.from(document.querySelectorAll('.invoice-checkbox:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى اختيار فاتورة واحدة على الأقل للطباعة');
        return;
    }
    // سيتم تنفيذ الطباعة لاحقاً
    alert('سيتم طباعة الفواتير قريباً');
}
</script>

<?php include 'includes/footer.php'; ?>
