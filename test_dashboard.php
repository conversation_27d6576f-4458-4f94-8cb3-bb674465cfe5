<?php
// اختبار لوحة التحكم
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار لوحة التحكم</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🧪 اختبار لوحة التحكم</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// محاكاة تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // افتراض وجود مستخدم بـ ID = 1
    echo "<div class='result info'>ℹ️ تم محاكاة تسجيل الدخول للمستخدم ID: 1</div>";
}

// اختبار الاتصال بقاعدة البيانات
echo "<h2>1. اختبار الاتصال بقاعدة البيانات</h2>";
try {
    $pdo = getDBConnection();
    if ($pdo) {
        echo "<div class='result success'>✅ الاتصال بقاعدة البيانات نجح</div>";
    } else {
        echo "<div class='result error'>❌ فشل الاتصال بقاعدة البيانات</div>";
        exit;
    }
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// اختبار الجداول المطلوبة
echo "<h2>2. فحص الجداول المطلوبة</h2>";
$requiredTables = ['users', 'clients', 'invoices', 'invoice_items'];
$missingTables = [];

foreach ($requiredTables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='result success'>✅ جدول '$table' موجود</div>";
        } else {
            echo "<div class='result error'>❌ جدول '$table' مفقود</div>";
            $missingTables[] = $table;
        }
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في فحص جدول '$table': " . $e->getMessage() . "</div>";
        $missingTables[] = $table;
    }
}

// إنشاء الجداول المفقودة
if (!empty($missingTables)) {
    echo "<h2>3. إنشاء الجداول المفقودة</h2>";
    
    $tableQueries = [
        'users' => "
        CREATE TABLE users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            company_name VARCHAR(100),
            phone VARCHAR(20),
            subscription_plan ENUM('free', 'basic', 'premium', 'enterprise') DEFAULT 'free',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'clients' => "
        CREATE TABLE clients (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            company VARCHAR(100),
            address TEXT,
            city VARCHAR(50),
            country VARCHAR(50),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'invoices' => "
        CREATE TABLE invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            client_id INT NOT NULL,
            invoice_number VARCHAR(50) UNIQUE NOT NULL,
            title VARCHAR(255),
            issue_date DATE NOT NULL,
            due_date DATE,
            status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
            subtotal DECIMAL(10,2) DEFAULT 0,
            tax_rate DECIMAL(5,2) DEFAULT 0,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'SAR',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        'invoice_items' => "
        CREATE TABLE invoice_items (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_id INT NOT NULL,
            description TEXT NOT NULL,
            quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];
    
    foreach ($missingTables as $table) {
        if (isset($tableQueries[$table])) {
            try {
                $pdo->exec($tableQueries[$table]);
                echo "<div class='result success'>✅ تم إنشاء جدول '$table'</div>";
            } catch (Exception $e) {
                echo "<div class='result error'>❌ خطأ في إنشاء جدول '$table': " . $e->getMessage() . "</div>";
            }
        }
    }
}

// اختبار وجود المستخدم
echo "<h2>4. اختبار بيانات المستخدم</h2>";
try {
    $user = getCurrentUser();
    if ($user) {
        echo "<div class='result success'>✅ تم العثور على بيانات المستخدم</div>";
        echo "<div class='result info'>👤 المستخدم: " . $user['first_name'] . " " . $user['last_name'] . "</div>";
        echo "<div class='result info'>📧 البريد: " . $user['email'] . "</div>";
    } else {
        echo "<div class='result warning'>⚠️ لم يتم العثور على بيانات المستخدم</div>";
        
        // إنشاء مستخدم تجريبي
        try {
            $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, first_name, last_name, company_name) 
                VALUES ('admin', '<EMAIL>', ?, 'المدير', 'العام', 'شركة تجريبية')
                ON DUPLICATE KEY UPDATE id=id
            ");
            $stmt->execute([$hashedPassword]);
            echo "<div class='result success'>✅ تم إنشاء/تحديث المستخدم التجريبي</div>";
        } catch (Exception $e) {
            echo "<div class='result error'>❌ خطأ في إنشاء المستخدم: " . $e->getMessage() . "</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في جلب بيانات المستخدم: " . $e->getMessage() . "</div>";
}

// اختبار استعلامات لوحة التحكم
echo "<h2>5. اختبار استعلامات لوحة التحكم</h2>";
$user_id = $_SESSION['user_id'];

// اختبار إحصائيات الفواتير
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
            COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_invoices,
            COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_invoices,
            SUM(CASE WHEN status = 'paid' THEN total_amount ELSE 0 END) as total_revenue,
            SUM(CASE WHEN status != 'paid' THEN total_amount ELSE 0 END) as pending_amount
        FROM invoices 
        WHERE user_id = ?
    ");
    $stmt->execute([$user_id]);
    $stats = $stmt->fetch();
    echo "<div class='result success'>✅ استعلام إحصائيات الفواتير نجح</div>";
    echo "<div class='result info'>📊 إجمالي الفواتير: " . $stats['total_invoices'] . "</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في استعلام الإحصائيات: " . $e->getMessage() . "</div>";
}

// اختبار إحصائيات العملاء
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_clients FROM clients WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $clientCount = $stmt->fetch()['total_clients'];
    echo "<div class='result success'>✅ استعلام إحصائيات العملاء نجح</div>";
    echo "<div class='result info'>👥 عدد العملاء: " . $clientCount . "</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في استعلام العملاء: " . $e->getMessage() . "</div>";
}

// إنشاء بيانات تجريبية إذا لم تكن موجودة
echo "<h2>6. إنشاء بيانات تجريبية</h2>";
try {
    // إنشاء عميل تجريبي
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
    $stmt->execute([$user_id]);
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("
            INSERT INTO clients (user_id, name, email, phone, company, address, city, country) 
            VALUES (?, 'أحمد محمد', '<EMAIL>', '0501234567', 'شركة العميل', 'شارع الملك فهد', 'الرياض', 'السعودية')
        ");
        $stmt->execute([$user_id]);
        echo "<div class='result success'>✅ تم إنشاء عميل تجريبي</div>";
    }
    
    // إنشاء فاتورة تجريبية
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM invoices WHERE user_id = ?");
    $stmt->execute([$user_id]);
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("SELECT id FROM clients WHERE user_id = ? LIMIT 1");
        $stmt->execute([$user_id]);
        $client_id = $stmt->fetchColumn();
        
        if ($client_id) {
            $stmt = $pdo->prepare("
                INSERT INTO invoices (user_id, client_id, invoice_number, title, issue_date, due_date, status, total_amount) 
                VALUES (?, ?, 'INV-2024-001', 'فاتورة تجريبية', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'sent', 1500.00)
            ");
            $stmt->execute([$user_id, $client_id]);
            echo "<div class='result success'>✅ تم إنشاء فاتورة تجريبية</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='result warning'>⚠️ تحذير في إنشاء البيانات التجريبية: " . $e->getMessage() . "</div>";
}

echo "<h2>7. النتيجة النهائية</h2>";
echo "<div class='result success'>";
echo "<h3>🎉 اختبار لوحة التحكم مكتمل!</h3>";
echo "<p>يمكنك الآن الوصول إلى لوحة التحكم</p>";
echo "</div>";

echo "<h3>الروابط:</h3>";
echo "<p><a href='dashboard.php' target='_blank'>🏠 لوحة التحكم</a></p>";
echo "<p><a href='auth/login.php' target='_blank'>🔐 تسجيل الدخول</a></p>";
echo "<p><a href='index.php' target='_blank'>🌐 الصفحة الرئيسية</a></p>";

echo "<div class='result info'>";
echo "<strong>بيانات الدخول:</strong><br>";
echo "اسم المستخدم: admin<br>";
echo "كلمة المرور: 123456";
echo "</div>";

echo "</body></html>";
?>
