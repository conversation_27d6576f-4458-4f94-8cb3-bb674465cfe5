<?php
// ملف اختبار بسيط
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار النظام</h1>";

// اختبار PHP
echo "<h2>معلومات PHP</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات</h2>";
try {
    $host = 'localhost';
    $dbname = 'invoice_saas';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    
    // اختبار جدول المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $count = $stmt->fetchColumn();
    echo "عدد المستخدمين: $count<br>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>وجود قاعدة البيانات 'invoice_saas'</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "</ul>";
}

// اختبار الملفات
echo "<h2>اختبار الملفات</h2>";
$files = [
    'config/config.php',
    'config/database.php',
    'includes/functions.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

echo "<h2>اختبار المجلدات</h2>";
$dirs = [
    'assets',
    'assets/css',
    'assets/js',
    'assets/images'
];

foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        echo "✅ $dir موجود<br>";
    } else {
        echo "❌ $dir غير موجود<br>";
    }
}

echo "<h2>معلومات الخادم</h2>";
echo "خادم الويب: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف') . "<br>";
echo "المجلد الحالي: " . getcwd() . "<br>";
echo "الوقت: " . date('Y-m-d H:i:s') . "<br>";

echo "<h2>اختبار الجلسة</h2>";
session_start();
$_SESSION['test'] = 'تم الاختبار بنجاح';
echo "الجلسة تعمل: " . $_SESSION['test'] . "<br>";

echo "<h2>الخطوات التالية</h2>";
echo "<ol>";
echo "<li>إذا كانت قاعدة البيانات لا تعمل، تأكد من تشغيل XAMPP/WAMP</li>";
echo "<li>إذا كانت الملفات مفقودة، تأكد من رفع جميع الملفات</li>";
echo "<li>إذا كان كل شيء يعمل، جرب الوصول إلى index.php</li>";
echo "</ol>";

echo "<p><a href='index.php'>اختبار الصفحة الرئيسية</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
h1, h2 {
    color: #333;
}
ul, ol {
    text-align: right;
}
</style>
