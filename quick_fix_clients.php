<?php
// إصلاح سريع لمشاكل بيانات العملاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح سريع - بيانات العملاء</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-danger { background: #dc3545; }";
echo ".btn-warning { background: #ffc107; color: #212529; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>⚡ إصلاح سريع - بيانات العملاء</h1>";

// تحميل التكوين
try {
    require_once 'config/config.php';
    echo "<div class='result success'>✅ تم تحميل التكوين</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في التكوين: " . $e->getMessage() . "</div>";
    exit;
}

// الاتصال بقاعدة البيانات
try {
    $pdo = getDBConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// تسجيل الدخول التلقائي
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
    $user = $stmt->fetch();
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        echo "<div class='result info'>تم تسجيل الدخول للمستخدم ID: " . $user['id'] . "</div>";
    }
}

$currentUserId = $_SESSION['user_id'] ?? 1;

echo "<h2>🔧 تطبيق الإصلاحات السريعة</h2>";

try {
    // 1. التأكد من وجود عمود user_id
    echo "<div class='result info'>🔄 فحص عمود user_id...</div>";
    $stmt = $pdo->query("DESCRIBE clients");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('user_id', $columns)) {
        echo "<div class='result warning'>⚠️ عمود user_id مفقود - سيتم إضافته</div>";
        $pdo->exec("ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
        $pdo->exec("ALTER TABLE clients ADD INDEX idx_user_id (user_id)");
        echo "<div class='result success'>✅ تم إضافة عمود user_id</div>";
    } else {
        echo "<div class='result success'>✅ عمود user_id موجود</div>";
    }
    
    // 2. فحص وجود بيانات
    echo "<div class='result info'>🔄 فحص البيانات الموجودة...</div>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
    $totalClients = $stmt->fetchColumn();
    
    if ($totalClients == 0) {
        echo "<div class='result warning'>⚠️ لا يوجد عملاء - سيتم إضافة عملاء تجريبيين</div>";
        
        $sampleClients = [
            [$currentUserId, 'أحمد محمد علي', '<EMAIL>', '0501234567', 'شركة التقنية المتقدمة'],
            [$currentUserId, 'فاطمة سالم أحمد', '<EMAIL>', '0509876543', 'مؤسسة الإبداع التجاري'],
            [$currentUserId, 'محمد عبدالله سعد', '<EMAIL>', '0551234567', 'شركة الحلول الذكية'],
            [$currentUserId, 'نورا خالد محمد', '<EMAIL>', '0561234567', 'مجموعة الأعمال المتكاملة'],
            [$currentUserId, 'سارة أحمد محمد', '<EMAIL>', '0571234567', 'شركة الاستشارات المتخصصة']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO clients (user_id, name, email, phone, company, is_active)
            VALUES (?, ?, ?, ?, ?, 1)
        ");
        
        $added = 0;
        foreach ($sampleClients as $client) {
            try {
                $stmt->execute($client);
                $added++;
            } catch (Exception $e) {
                // تجاهل الأخطاء
            }
        }
        
        echo "<div class='result success'>✅ تم إضافة $added عميل تجريبي</div>";
    } else {
        echo "<div class='result success'>✅ يوجد $totalClients عميل في قاعدة البيانات</div>";
    }
    
    // 3. التأكد من ربط العملاء بالمستخدمين
    echo "<div class='result info'>🔄 فحص ربط العملاء بالمستخدمين...</div>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
    $stmt->execute([$currentUserId]);
    $userClients = $stmt->fetchColumn();
    
    if ($userClients == 0) {
        echo "<div class='result warning'>⚠️ المستخدم الحالي ليس له عملاء - سيتم الربط</div>";
        
        // ربط أول 3 عملاء بالمستخدم الحالي
        $stmt = $pdo->prepare("UPDATE clients SET user_id = ? WHERE id IN (SELECT id FROM (SELECT id FROM clients ORDER BY id ASC LIMIT 3) as temp)");
        $stmt->execute([$currentUserId]);
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM clients WHERE user_id = ?");
        $stmt->execute([$currentUserId]);
        $newCount = $stmt->fetchColumn();
        
        echo "<div class='result success'>✅ تم ربط $newCount عميل بالمستخدم الحالي</div>";
    } else {
        echo "<div class='result success'>✅ المستخدم الحالي له $userClients عميل</div>";
    }
    
    // 4. تفعيل جميع العملاء
    echo "<div class='result info'>🔄 تفعيل العملاء...</div>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients WHERE is_active = 0");
    $inactiveCount = $stmt->fetchColumn();
    
    if ($inactiveCount > 0) {
        $pdo->exec("UPDATE clients SET is_active = 1");
        echo "<div class='result success'>✅ تم تفعيل $inactiveCount عميل</div>";
    } else {
        echo "<div class='result success'>✅ جميع العملاء نشطين</div>";
    }
    
    // 5. إصلاح القيم الفارغة في user_id
    echo "<div class='result info'>🔄 إصلاح القيم الفارغة...</div>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients WHERE user_id IS NULL OR user_id = 0");
    $nullCount = $stmt->fetchColumn();
    
    if ($nullCount > 0) {
        $stmt = $pdo->prepare("UPDATE clients SET user_id = ? WHERE user_id IS NULL OR user_id = 0");
        $stmt->execute([$currentUserId]);
        echo "<div class='result success'>✅ تم إصلاح $nullCount سجل بقيم فارغة</div>";
    } else {
        echo "<div class='result success'>✅ لا توجد قيم فارغة في user_id</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الإصلاح: " . $e->getMessage() . "</div>";
}

echo "<h2>🧪 اختبار النتائج</h2>";

try {
    // اختبار الاستعلام من clients.php
    $where_conditions = ["c.user_id = ? AND c.is_active = 1"];
    $params = [$currentUserId];
    $where_clause = implode(' AND ', $where_conditions);
    
    // استعلام العد
    $countSql = "SELECT COUNT(*) FROM clients c WHERE $where_clause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $count = $countStmt->fetchColumn();
    
    echo "<div class='result info'>استعلام العد: $count عميل للمستخدم الحالي</div>";
    
    if ($count > 0) {
        // الاستعلام الرئيسي
        $sql = "
            SELECT c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, 
                   c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at,
                   COALESCE(COUNT(i.id), 0) as invoice_count,
                   COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END), 0) as total_paid,
                   COALESCE(SUM(CASE WHEN i.status != 'paid' AND i.status IS NOT NULL THEN i.total_amount ELSE 0 END), 0) as total_pending,
                   MAX(i.created_at) as last_invoice_date
            FROM clients c 
            LEFT JOIN invoices i ON c.id = i.client_id
            WHERE $where_clause 
            GROUP BY c.id, c.user_id, c.name, c.email, c.phone, c.company, c.address, c.city, c.country, c.tax_number, c.notes, c.is_active, c.created_at, c.updated_at
            ORDER BY c.name ASC 
            LIMIT 5
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $clients = $stmt->fetchAll();
        
        echo "<div class='result success'>✅ الاستعلام الرئيسي نجح: " . count($clients) . " عميل</div>";
        
        if (!empty($clients)) {
            echo "<div class='result info'>";
            echo "<strong>عينة من العملاء المسترجعة:</strong><br>";
            foreach ($clients as $client) {
                echo "- " . htmlspecialchars($client['name']) . " (ID: " . $client['id'] . ", user_id: " . $client['user_id'] . ")<br>";
            }
            echo "</div>";
            
            echo "<div class='result success'>";
            echo "<h3>🎉 تم إصلاح جميع المشاكل!</h3>";
            echo "<p>صفحة العملاء ستعمل الآن بشكل صحيح</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='result error'>❌ لا يزال لا يوجد عملاء للمستخدم الحالي</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار النتائج: " . $e->getMessage() . "</div>";
}

echo "<h2>📊 ملخص الإصلاحات</h2>";
echo "<div class='result success'>";
echo "<h3>✅ تم تطبيق الإصلاحات التالية:</h3>";
echo "<ul>";
echo "<li>✅ التأكد من وجود عمود user_id</li>";
echo "<li>✅ إضافة عملاء تجريبيين إذا لم تكن موجودة</li>";
echo "<li>✅ ربط العملاء بالمستخدم الحالي</li>";
echo "<li>✅ تفعيل جميع العملاء</li>";
echo "<li>✅ إصلاح القيم الفارغة في user_id</li>";
echo "<li>✅ اختبار الاستعلامات</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🚀 اختبار الصفحات:</h3>";
echo "<a href='clients.php' class='btn btn-success' target='_blank'>🔗 صفحة العملاء</a>";
echo "<a href='dashboard.php' class='btn' target='_blank'>🔗 لوحة التحكم</a>";
echo "<a href='create-invoice.php' class='btn' target='_blank'>🔗 إنشاء فاتورة</a>";

echo "<div class='result info'>";
echo "<strong>بيانات الدخول:</strong><br>";
echo "اسم المستخدم: admin<br>";
echo "كلمة المرور: 123456";
echo "</div>";

echo "<div class='result warning'>";
echo "<strong>ملاحظة:</strong> إذا كانت المشكلة محلولة، يمكنك حذف ملفات الإصلاح هذه.";
echo "</div>";

echo "</body></html>";
?>
