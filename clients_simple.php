<?php
// Simple clients page for testing
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'config/config.php';

// Check authentication
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getDBConnection();
        
        if (isset($_POST['action']) && $_POST['action'] == 'add_client') {
            $name = sanitize($_POST['name']);
            $email = sanitize($_POST['email']);
            $phone = sanitize($_POST['phone']);
            $company = sanitize($_POST['company']);
            
            if (empty($name)) {
                throw new Exception('اسم العميل مطلوب');
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO clients (user_id, name, email, phone, company, is_active)
                VALUES (?, ?, ?, ?, ?, 1)
            ");
            
            $stmt->execute([$user_id, $name, $email, $phone, $company]);
            $success = 'تم إضافة العميل بنجاح';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get clients with simple query
try {
    $pdo = getDBConnection();
    
    $stmt = $pdo->prepare("
        SELECT * FROM clients 
        WHERE user_id = ? AND is_active = 1 
        ORDER BY name ASC
    ");
    $stmt->execute([$user_id]);
    $clients = $stmt->fetchAll();
    
} catch (Exception $e) {
    $clients = [];
    $error = "خطأ في جلب بيانات العملاء: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نسخة مبسطة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .client-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .client-name { font-weight: bold; color: #333; margin-bottom: 10px; }
        .client-info { color: #666; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-users me-2"></i>إدارة العملاء - نسخة مبسطة</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                        <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Messages -->
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Clients List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">قائمة العملاء (<?php echo count($clients); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($clients)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5>لا يوجد عملاء</h5>
                                <p class="text-muted">لم يتم العثور على أي عملاء</p>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                                    <i class="fas fa-plus me-1"></i>إضافة عميل جديد
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($clients as $client): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="client-card">
                                            <div class="client-name">
                                                <i class="fas fa-user me-2"></i>
                                                <?php echo htmlspecialchars($client['name']); ?>
                                            </div>
                                            
                                            <div class="client-info">
                                                <?php if ($client['company']): ?>
                                                    <div><i class="fas fa-building me-2"></i><?php echo htmlspecialchars($client['company']); ?></div>
                                                <?php endif; ?>
                                                
                                                <?php if ($client['email']): ?>
                                                    <div><i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($client['email']); ?></div>
                                                <?php endif; ?>
                                                
                                                <?php if ($client['phone']): ?>
                                                    <div><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($client['phone']); ?></div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="mt-3">
                                                <small class="text-muted">
                                                    تم الإنشاء: <?php echo formatDate($client['created_at'], 'd/m/Y'); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add Client Modal -->
    <div class="modal fade" id="addClientModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_client">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                        
                        <div class="mb-3">
                            <label for="company" class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" id="company" name="company">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ العميل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="container-fluid mt-4">
        <div class="text-center">
            <a href="clients.php" class="btn btn-outline-primary">العودة للصفحة الأصلية</a>
            <a href="dashboard.php" class="btn btn-outline-secondary">لوحة التحكم</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
